<?php

namespace App\Http\Controllers;

use App\Http\Requests\DepositRequest;
use App\Http\Requests\WithdrawalRequest;
use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

class WalletController extends Controller
{
    /**
     * Show the user's wallet balance page.
     */
    public function balance()
    {
        $user = Auth::user();

        return Inertia::render('wallet/balance', [
            'user' => $user,
            'balance' => $user->wallet_balance,
        ]);
    }

    /**
     * Show the transaction history page.
     */
    public function transactions()
    {
        $user = Auth::user();

        $transactions = $user->walletTransactions()
            ->latest()
            ->paginate(20);

        return Inertia::render('wallet/transactions', [
            'transactions' => $transactions,
            'user' => $user,
        ]);
    }

    /**
     * Show the add funds page.
     */
    public function showAddFunds()
    {
        return Inertia::render('wallet/add-funds', [
            'paystack_public_key' => config('services.paystack.public_key'),
        ]);
    }

    /**
     * Show the withdraw funds page.
     */
    public function showWithdraw()
    {
        $user = Auth::user();

        return Inertia::render('wallet/withdraw', [
            'user' => $user,
            'balance' => $user->wallet_balance,
        ]);
    }

    /**
     * Initialize Paystack payment for deposit.
     */
    public function initializeDeposit(DepositRequest $request)
    {
        $user = Auth::user();
        $amount = $request->amount * 100; // Convert to kobo
        $reference = 'dep_' . time() . '_' . $user->id;

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('services.paystack.secret_key'),
                'Content-Type' => 'application/json',
            ])->post('https://api.paystack.co/transaction/initialize', [
                'email' => $request->email,
                'amount' => $amount,
                'reference' => $reference,
                'callback_url' => route('wallet.deposit.callback'),
                'metadata' => [
                    'user_id' => $user->id,
                    'type' => 'deposit',
                ],
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Create pending transaction record
                WalletTransaction::create([
                    'user_id' => $user->id,
                    'transaction_id' => $reference,
                    'type' => 'deposit',
                    'amount' => $request->amount,
                    'balance_before' => $user->wallet_balance,
                    'balance_after' => $user->wallet_balance,
                    'status' => 'pending',
                    'payment_method' => 'paystack',
                    'payment_reference' => $reference,
                    'description' => 'Wallet deposit via Paystack',
                    'metadata' => [
                        'paystack_reference' => $reference,
                        'authorization_url' => $data['data']['authorization_url'],
                    ],
                ]);

                // Redirect directly to Paystack payment page
                return redirect()->away($data['data']['authorization_url']);
            }

            return back()->withErrors(['error' => 'Failed to initialize payment']);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Payment initialization failed']);
        }
    }

    /**
     * Handle Paystack payment callback.
     */
    public function handleDepositCallback(Request $request)
    {
        $reference = $request->query('reference');

        if (!$reference) {
            return redirect()->route('wallet.add-funds')->with('error', 'Invalid payment reference');
        }

        try {
            // Verify payment with Paystack
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('services.paystack.secret_key'),
            ])->get("https://api.paystack.co/transaction/verify/{$reference}");

            if ($response->successful()) {
                $data = $response->json();

                if ($data['data']['status'] === 'success') {
                    // Find the transaction
                    $transaction = WalletTransaction::where('payment_reference', $reference)
                        ->where('status', 'pending')
                        ->first();

                    if ($transaction) {
                        DB::transaction(function () use ($transaction, $data) {
                            $user = $transaction->user;
                            $amount = $data['data']['amount'] / 100; // Convert from kobo

                            // Update user balance
                            $newBalance = $user->wallet_balance + $amount;
                            $user->update(['wallet_balance' => $newBalance]);

                            // Update transaction
                            $transaction->update([
                                'status' => 'completed',
                                'balance_after' => $newBalance,
                                'metadata' => array_merge($transaction->metadata ?? [], [
                                    'paystack_response' => $data['data'],
                                    'completed_at' => now(),
                                ]),
                            ]);
                        });

                        return redirect()->route('wallet.transactions')->with('success', 'Deposit successful! Your wallet has been credited.');
                    }
                }
            }

            return redirect()->route('wallet.add-funds')->with('error', 'Payment verification failed');
        } catch (\Exception $e) {
            return redirect()->route('wallet.add-funds')->with('error', 'Payment processing failed');
        }
    }

    /**
     * Process withdrawal request.
     */
    public function processWithdrawal(WithdrawalRequest $request)
    {
        $user = Auth::user();
        $amount = $request->amount;

        try {
            DB::transaction(function () use ($user, $amount, $request) {
                // Update user balance
                $newBalance = $user->wallet_balance - $amount;
                $user->update(['wallet_balance' => $newBalance]);

                // Create withdrawal transaction
                WalletTransaction::create([
                    'user_id' => $user->id,
                    'transaction_id' => 'with_' . time() . '_' . $user->id,
                    'type' => 'withdrawal',
                    'amount' => $amount,
                    'balance_before' => $user->wallet_balance + $amount,
                    'balance_after' => $newBalance,
                    'status' => 'pending',
                    'payment_method' => 'bank_transfer',
                    'description' => 'Wallet withdrawal to bank account',
                    'metadata' => [
                        'bank_name' => $request->bank_name,
                        'account_number' => $request->account_number,
                        'account_name' => $request->account_name,
                        'requested_at' => now(),
                    ],
                ]);
            });

            return redirect()->route('wallet.transactions')->with('success', 'Withdrawal request submitted successfully. Processing may take 1-3 business days.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Withdrawal processing failed. Please try again.']);
        }
    }
}
