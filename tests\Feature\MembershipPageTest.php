<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MembershipPageTest extends TestCase
{
    /**
     * Test that the membership page loads successfully.
     */
    public function test_membership_page_loads_successfully(): void
    {
        $response = $this->get('/membership');

        $response->assertStatus(200);
        $response->assertInertia(
            fn($page) =>
            $page->component('membership')
        );
    }

    /**
     * Test that the membership page is accessible without authentication.
     */
    public function test_membership_page_accessible_without_auth(): void
    {
        $response = $this->get(route('membership'));

        $response->assertStatus(200);
    }

    /**
     * Test that the membership route exists and has correct name.
     */
    public function test_membership_route_exists(): void
    {
        $this->assertTrue(route('membership') !== null);
        $this->assertEquals(url('/membership'), route('membership'));
    }
}
