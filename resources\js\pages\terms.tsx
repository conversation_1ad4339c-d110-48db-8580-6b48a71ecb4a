import { Head } from '@inertiajs/react';

export default function Terms() {
    return (
        <>
            <Head title="Terms and Conditions" />

            <div className="min-h-screen bg-white dark:bg-gray-900">
                <div className="container mx-auto px-4 py-12">
                    <div className="prose dark:prose-invert max-w-none">
                        <h1 className="mb-8 text-3xl font-bold text-gray-900 dark:text-white">Terms and Conditions</h1>

                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            Welcome to Thesylink. These Terms and Conditions ("Terms") govern your access to and use of our services. By registering
                            an account or using our platform you agree to be bound by these Terms.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">1. Acceptance</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            By using Thesylink, you accept and agree to be bound by these Terms and any additional terms that may apply to specific
                            features or services.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">2. Accounts</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            You agree to provide accurate and complete information when creating an account and to keep your account information
                            updated. You are responsible for activity that occurs through your account.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">3. User Conduct</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            Users must not use the service for unlawful purposes, to post or transmit harmful or abusive content, or to infringe the
                            rights of others. You agree to act in good faith when using the platform.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">4. Payments and Fees</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            Any payments processed through the platform are subject to the terms of our payment providers. The platform may charge
                            fees or commissions; details are available in the Fees section of the site.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">5. Intellectual Property</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            All content provided by Thesylink is protected by copyright and other intellectual property laws. You may not reproduce or
                            redistribute platform content without prior written permission.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">6. Termination</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            We may suspend or terminate accounts that violate these Terms or for other legitimate reasons. Upon termination, your
                            access to the services will cease.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">7. Liability</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            To the extent permitted by law, Thesylink will not be liable for indirect, incidental, special, or consequential damages
                            arising out of your use of the service.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">8. Changes to Terms</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            We may update these Terms from time to time. Continued use of the service constitutes acceptance of updated Terms.
                        </p>

                        <h2 className="mt-8 mb-4 text-2xl font-semibold text-gray-900 dark:text-white">9. Contact</h2>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">
                            If you have questions about these Terms, contact <NAME_EMAIL>.
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
