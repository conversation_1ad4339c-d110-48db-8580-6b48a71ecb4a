import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, CheckCircle, Clock, DollarSign, FileText, Play, Send, User } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface Milestone {
    id: number;
    title: string;
    description: string;
    order_index: number;
    payment_percentage: number;
    payment_amount: number;
    status: 'pending' | 'in_progress' | 'submitted' | 'approved' | 'paid';
    started_at: string | null;
    submitted_at: string | null;
    approved_at: string | null;
    paid_at: string | null;
    submission_notes: string | null;
    approval_notes: string | null;
}

interface Project {
    id: number;
    title: string;
    slug: string;
    status: string;
    accepted_bid_amount: number;
    total_milestones: number;
    completed_milestones: number;
    escrow_status: string;
    user: User;
    assigned_freelancer: User;
    milestones: Milestone[];
}

interface Props {
    project: Project;
    isFreelancer: boolean;
    isClient: boolean;
}

export default function Milestones({ project, isFreelancer, isClient }: Props) {
    const [selectedMilestone, setSelectedMilestone] = useState<Milestone | null>(null);
    const [showSubmissionForm, setShowSubmissionForm] = useState(false);
    const [showApprovalForm, setShowApprovalForm] = useState(false);
    const [showRevisionForm, setShowRevisionForm] = useState(false);

    const { data, setData, post, patch, processing, errors, reset } = useForm({
        submission_notes: '',
        approval_notes: '',
        revision_notes: '',
    });

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending':
                return 'secondary';
            case 'in_progress':
                return 'default';
            case 'submitted':
                return 'outline';
            case 'approved':
                return 'default';
            case 'paid':
                return 'default';
            default:
                return 'secondary';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4" />;
            case 'in_progress':
                return <Play className="h-4 w-4" />;
            case 'submitted':
                return <Send className="h-4 w-4" />;
            case 'approved':
                return <CheckCircle className="h-4 w-4" />;
            case 'paid':
                return <DollarSign className="h-4 w-4" />;
            default:
                return <Clock className="h-4 w-4" />;
        }
    };

    const handleStartMilestone = (milestone: Milestone) => {
        patch(route('milestones.start', { project: project.slug, milestone: milestone.id }));
    };

    const handleSubmitMilestone = (milestone: Milestone) => {
        patch(route('milestones.submit', { project: project.slug, milestone: milestone.id }), {
            data: { submission_notes: data.submission_notes },
            onSuccess: () => {
                setShowSubmissionForm(false);
                setSelectedMilestone(null);
                reset('submission_notes');
            },
        });
    };

    const handleApproveMilestone = (milestone: Milestone) => {
        patch(route('milestones.approve', { project: project.slug, milestone: milestone.id }), {
            data: { approval_notes: data.approval_notes },
            onSuccess: () => {
                setShowApprovalForm(false);
                setSelectedMilestone(null);
                reset('approval_notes');
            },
        });
    };

    const handleRequestRevision = (milestone: Milestone) => {
        patch(route('milestones.revision', { project: project.slug, milestone: milestone.id }), {
            data: { revision_notes: data.revision_notes },
            onSuccess: () => {
                setShowRevisionForm(false);
                setSelectedMilestone(null);
                reset('revision_notes');
            },
        });
    };

    const progressPercentage = (project.completed_milestones / project.total_milestones) * 100;

    return (
        <>
            <Head title={`${project.title} - Milestones`} />

            <div className="min-h-screen bg-gray-50 py-8">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="mb-4 flex items-center gap-4">
                            <Link
                                href={route('projects.show', project.slug)}
                                className="flex items-center text-sm text-muted-foreground hover:text-foreground"
                            >
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Project
                            </Link>
                        </div>

                        <div className="flex items-start justify-between">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">{project.title}</h1>
                                <p className="mt-2 text-lg text-gray-600">Chapter Milestones</p>
                            </div>
                            <Badge variant="outline" className="text-sm">
                                {project.status.replace('_', ' ').toUpperCase()}
                            </Badge>
                        </div>

                        {/* Progress Overview */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    Project Progress
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div>
                                        <div className="mb-2 flex justify-between text-sm">
                                            <span>Completed Milestones</span>
                                            <span>
                                                {project.completed_milestones} of {project.total_milestones}
                                            </span>
                                        </div>
                                        <Progress value={progressPercentage} className="h-2" />
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 border-t pt-4 md:grid-cols-3">
                                        <div className="flex items-center gap-2">
                                            <DollarSign className="h-4 w-4 text-green-600" />
                                            <div>
                                                <p className="text-sm font-medium">Total Value</p>
                                                <p className="text-lg font-bold">₵{Number(project.accepted_bid_amount || 0).toFixed(2)}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <User className="h-4 w-4 text-blue-600" />
                                            <div>
                                                <p className="text-sm font-medium">{isFreelancer ? 'Client' : 'Freelancer'}</p>
                                                <p className="text-sm text-gray-600">
                                                    {isFreelancer ? project.user.name : project.assigned_freelancer.name}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-purple-600" />
                                            <div>
                                                <p className="text-sm font-medium">Escrow Status</p>
                                                <p className="text-sm text-gray-600 capitalize">{project.escrow_status.replace('_', ' ')}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Milestones List */}
                    <div className="space-y-4">
                        {project.milestones.map((milestone) => (
                            <Card key={milestone.id} className="overflow-hidden">
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="flex items-center gap-2">
                                                {getStatusIcon(milestone.status)}
                                                <CardTitle className="text-lg">{milestone.title}</CardTitle>
                                            </div>
                                            <Badge variant={getStatusColor(milestone.status)}>{milestone.status.replace('_', ' ')}</Badge>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm text-muted-foreground">Payment</p>
                                            <p className="font-semibold">₵{Number(milestone.payment_amount || 0).toFixed(2)}</p>
                                            <p className="text-xs text-muted-foreground">{Number(milestone.payment_percentage || 0).toFixed(1)}%</p>
                                        </div>
                                    </div>
                                    <CardDescription>{milestone.description}</CardDescription>
                                </CardHeader>

                                <CardContent>
                                    <div className="space-y-4">
                                        {/* Timeline */}
                                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                            {milestone.started_at && (
                                                <div className="flex items-center gap-1">
                                                    <Calendar className="h-3 w-3" />
                                                    Started: {new Date(milestone.started_at).toLocaleDateString()}
                                                </div>
                                            )}
                                            {milestone.submitted_at && (
                                                <div className="flex items-center gap-1">
                                                    <Send className="h-3 w-3" />
                                                    Submitted: {new Date(milestone.submitted_at).toLocaleDateString()}
                                                </div>
                                            )}
                                            {milestone.approved_at && (
                                                <div className="flex items-center gap-1">
                                                    <CheckCircle className="h-3 w-3" />
                                                    Approved: {new Date(milestone.approved_at).toLocaleDateString()}
                                                </div>
                                            )}
                                        </div>

                                        {/* Notes */}
                                        {milestone.submission_notes && (
                                            <div className="rounded-lg bg-blue-50 p-3">
                                                <p className="mb-1 text-sm font-medium text-blue-900">Submission Notes:</p>
                                                <p className="text-sm text-blue-800">{milestone.submission_notes}</p>
                                            </div>
                                        )}

                                        {milestone.approval_notes && (
                                            <div className="rounded-lg bg-green-50 p-3">
                                                <p className="mb-1 text-sm font-medium text-green-900">
                                                    {milestone.status === 'in_progress' ? 'Revision Notes:' : 'Approval Notes:'}
                                                </p>
                                                <p className="text-sm text-green-800">{milestone.approval_notes}</p>
                                            </div>
                                        )}

                                        {/* Action Buttons */}
                                        <div className="flex gap-2 border-t pt-2">
                                            {/* Freelancer Actions */}
                                            {isFreelancer && (
                                                <>
                                                    {milestone.status === 'pending' && (
                                                        <Button onClick={() => handleStartMilestone(milestone)} disabled={processing} size="sm">
                                                            <Play className="mr-2 h-4 w-4" />
                                                            Start Chapter
                                                        </Button>
                                                    )}

                                                    {milestone.status === 'in_progress' && (
                                                        <Button
                                                            onClick={() => {
                                                                setSelectedMilestone(milestone);
                                                                setShowSubmissionForm(true);
                                                            }}
                                                            disabled={processing}
                                                            size="sm"
                                                        >
                                                            <Send className="mr-2 h-4 w-4" />
                                                            Submit for Review
                                                        </Button>
                                                    )}
                                                </>
                                            )}

                                            {/* Client Actions */}
                                            {isClient && milestone.status === 'submitted' && (
                                                <>
                                                    <Button
                                                        onClick={() => {
                                                            setSelectedMilestone(milestone);
                                                            setShowApprovalForm(true);
                                                        }}
                                                        disabled={processing}
                                                        size="sm"
                                                    >
                                                        <CheckCircle className="mr-2 h-4 w-4" />
                                                        Approve & Release Payment
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        onClick={() => {
                                                            setSelectedMilestone(milestone);
                                                            setShowRevisionForm(true);
                                                        }}
                                                        disabled={processing}
                                                        size="sm"
                                                    >
                                                        Request Revision
                                                    </Button>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </div>

            {/* Submission Form Modal */}
            {showSubmissionForm && selectedMilestone && (
                <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4">
                    <Card className="w-full max-w-md">
                        <CardHeader>
                            <CardTitle>Submit {selectedMilestone.title}</CardTitle>
                            <CardDescription>Provide details about your completed work for client review.</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="submission_notes">Submission Notes</Label>
                                <Textarea
                                    id="submission_notes"
                                    placeholder="Describe what you've completed, any challenges faced, and next steps..."
                                    value={data.submission_notes}
                                    onChange={(e) => setData('submission_notes', e.target.value)}
                                    rows={4}
                                />
                                {errors.submission_notes && <p className="mt-1 text-sm text-red-600">{errors.submission_notes}</p>}
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    onClick={() => handleSubmitMilestone(selectedMilestone)}
                                    disabled={processing || !data.submission_notes.trim()}
                                    className="flex-1"
                                >
                                    Submit for Review
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setShowSubmissionForm(false);
                                        setSelectedMilestone(null);
                                        reset('submission_notes');
                                    }}
                                    disabled={processing}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Approval Form Modal */}
            {showApprovalForm && selectedMilestone && (
                <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4">
                    <Card className="w-full max-w-md">
                        <CardHeader>
                            <CardTitle>Approve {selectedMilestone.title}</CardTitle>
                            <CardDescription>
                                Approve this milestone and release payment of ₵{Number(selectedMilestone.payment_amount || 0).toFixed(2)}.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="approval_notes">Approval Notes (Optional)</Label>
                                <Textarea
                                    id="approval_notes"
                                    placeholder="Any feedback or comments about the completed work..."
                                    value={data.approval_notes}
                                    onChange={(e) => setData('approval_notes', e.target.value)}
                                    rows={3}
                                />
                            </div>
                            <div className="flex gap-2">
                                <Button onClick={() => handleApproveMilestone(selectedMilestone)} disabled={processing} className="flex-1">
                                    Approve & Release Payment
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setShowApprovalForm(false);
                                        setSelectedMilestone(null);
                                        reset('approval_notes');
                                    }}
                                    disabled={processing}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Revision Request Form Modal */}
            {showRevisionForm && selectedMilestone && (
                <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4">
                    <Card className="w-full max-w-md">
                        <CardHeader>
                            <CardTitle>Request Revision for {selectedMilestone.title}</CardTitle>
                            <CardDescription>Provide specific feedback about what needs to be revised.</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="revision_notes">Revision Notes</Label>
                                <Textarea
                                    id="revision_notes"
                                    placeholder="Explain what needs to be changed or improved..."
                                    value={data.revision_notes}
                                    onChange={(e) => setData('revision_notes', e.target.value)}
                                    rows={4}
                                />
                                {errors.revision_notes && <p className="mt-1 text-sm text-red-600">{errors.revision_notes}</p>}
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    onClick={() => handleRequestRevision(selectedMilestone)}
                                    disabled={processing || !data.revision_notes.trim()}
                                    className="flex-1"
                                >
                                    Request Revision
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setShowRevisionForm(false);
                                        setSelectedMilestone(null);
                                        reset('revision_notes');
                                    }}
                                    disabled={processing}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}
        </>
    );
}
