<?php

namespace App\Listeners;

use App\Notifications\WelcomeUser;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendWelcomeEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        // Send welcome email to the newly registered user
        /** @var \App\Models\User $user */
        $user = $event->user;
        $user->notify(new WelcomeUser($user));
    }
}
