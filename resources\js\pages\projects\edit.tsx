import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { FileText, Upload, X } from 'lucide-react';
import { useState } from 'react';

interface Project {
    id: number;
    title: string;
    description: string;
    requirements?: string;
    budget_min?: number;
    budget_max?: number;
    budget_type: string;
    deadline?: string;
    category?: string;
    academic_level?: string;
    files: Array<{
        id: number;
        filename: string;
        original_name: string;
        file_path: string;
        mime_type: string;
        file_size: number;
    }>;
}

interface EditProjectProps {
    project: Project;
}

const categories = [
    'Computer Science',
    'Nursing',
    'Business Administration',
    'Engineering',
    'Psychology',
    'Education',
    'Medicine',
    'Law',
    'Mathematics',
    'Biology',
    'Chemistry',
    'Physics',
    'Literature',
    'History',
    'Other',
];

const academicLevels = ['High School', 'Bachelor', 'Master', 'PhD', 'Professional'];

const budgetTypes = [
    { value: 'fixed', label: 'Fixed Price' },
    { value: 'hourly', label: 'Hourly Rate' },
    { value: 'negotiable', label: 'Negotiable' },
];

interface FormData {
    title: string;
    description: string;
    requirements: string;
    budget_min: string;
    budget_max: string;
    budget_type: string;
    deadline: string;
    category: string;
    academic_level: string;
    files: File[];
}

export default function EditProject({ project }: EditProjectProps) {
    const { toast } = useToast();

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: project.title,
            href: `/projects/${project.id}`,
        },
        {
            title: 'Edit',
            href: `/projects/${project.id}/edit`,
        },
    ];

    const [formData, setFormData] = useState<FormData>({
        title: project.title || '',
        description: project.description || '',
        requirements: project.requirements || '',
        budget_min: project.budget_min ? project.budget_min.toString() : '',
        budget_max: project.budget_max ? project.budget_max.toString() : '',
        budget_type: project.budget_type || 'fixed',
        deadline: project.deadline || '',
        category: project.category || '',
        academic_level: project.academic_level || '',
        files: [],
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [processing, setProcessing] = useState(false);

    const handleInputChange = (field: keyof FormData, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: '' }));
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = Array.from(e.target.files || []);
        const validFiles = selectedFiles.filter((file) => {
            const validTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword'];
            return validTypes.includes(file.type) && file.size <= 10 * 1024 * 1024; // 10MB
        });

        setFormData((prev) => ({ ...prev, files: [...prev.files, ...validFiles].slice(0, 5) }));
    };

    const removeFile = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            files: prev.files.filter((_, i) => i !== index),
        }));
    };

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.title.trim()) newErrors.title = 'Title is required';
        if (!formData.description.trim()) newErrors.description = 'Description is required';
        if (!formData.category) newErrors.category = 'Category is required';
        if (!formData.academic_level) newErrors.academic_level = 'Academic level is required';

        if (formData.budget_type !== 'negotiable') {
            if (!formData.budget_min) {
                newErrors.budget_min = 'Minimum budget is required';
            } else if (parseFloat(formData.budget_min) <= 0) {
                newErrors.budget_min = 'Budget must be greater than 0';
            }

            if (formData.budget_max && parseFloat(formData.budget_max) < parseFloat(formData.budget_min)) {
                newErrors.budget_max = 'Maximum budget must be greater than minimum budget';
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            toast({
                title: 'Validation Error',
                description: 'Please fix the errors before submitting.',
                variant: 'error',
            });
            return;
        }

        setProcessing(true);

        try {
            const submitData = new FormData();

            // Add form fields
            Object.entries(formData).forEach(([key, value]) => {
                if (key === 'files') return; // Handle files separately
                submitData.append(key, value);
            });

            // Add files
            formData.files.forEach((file) => {
                submitData.append('files[]', file);
            });

            // Add method for Laravel
            submitData.append('_method', 'PUT');

            router.post(`/projects/${project.id}`, submitData, {
                onSuccess: () => {
                    toast({
                        title: 'Success',
                        description: 'Project updated successfully!',
                    });
                },
                onError: (errors) => {
                    console.error('Update errors:', errors);
                    setErrors(errors);
                    toast({
                        title: 'Error',
                        description: 'Failed to update project. Please try again.',
                        variant: 'error',
                    });
                },
                onFinish: () => setProcessing(false),
            });
        } catch (error) {
            console.error('Error updating project:', error);
            setProcessing(false);
            toast({
                title: 'Error',
                description: 'Failed to update project. Please try again.',
                variant: 'error',
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit ${project.title}`} />

            <div className="mx-auto max-w-4xl space-y-6">
                <div className="text-center">
                    <h1 className="text-3xl font-bold">Edit Project</h1>
                    <p className="mt-2 text-muted-foreground">Update your project details</p>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Project Details</CardTitle>
                        <CardDescription>Update the information about your project</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Title */}
                            <div className="space-y-2">
                                <Label htmlFor="title">Project Title *</Label>
                                <Input
                                    id="title"
                                    value={formData.title}
                                    onChange={(e) => handleInputChange('title', e.target.value)}
                                    placeholder="Enter a descriptive title for your project"
                                    className={errors.title ? 'border-red-500' : ''}
                                />
                                {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description">Project Description *</Label>
                                <Textarea
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) => handleInputChange('description', e.target.value)}
                                    placeholder="Provide a detailed description of your project..."
                                    rows={4}
                                    className={errors.description ? 'border-red-500' : ''}
                                />
                                {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                            </div>

                            {/* Requirements */}
                            <div className="space-y-2">
                                <Label htmlFor="requirements">Specific Requirements</Label>
                                <Textarea
                                    id="requirements"
                                    value={formData.requirements}
                                    onChange={(e) => handleInputChange('requirements', e.target.value)}
                                    placeholder="List any specific requirements, guidelines, or expectations..."
                                    rows={3}
                                />
                            </div>

                            {/* Category and Level */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="category">Category *</Label>
                                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                                        <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {categories.map((category) => (
                                                <SelectItem key={category} value={category}>
                                                    {category}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="academic_level">Academic Level *</Label>
                                    <Select value={formData.academic_level} onValueChange={(value) => handleInputChange('academic_level', value)}>
                                        <SelectTrigger className={errors.academic_level ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select level" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {academicLevels.map((level) => (
                                                <SelectItem key={level} value={level}>
                                                    {level}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.academic_level && <p className="text-sm text-red-500">{errors.academic_level}</p>}
                                </div>
                            </div>

                            {/* Budget */}
                            <div className="space-y-4">
                                <Label>Budget Information</Label>

                                <div className="space-y-2">
                                    <Label htmlFor="budget_type">Budget Type</Label>
                                    <Select value={formData.budget_type} onValueChange={(value) => handleInputChange('budget_type', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {budgetTypes.map((type) => (
                                                <SelectItem key={type.value} value={type.value}>
                                                    {type.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {formData.budget_type !== 'negotiable' && (
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="budget_min">Minimum Budget *</Label>
                                            <Input
                                                id="budget_min"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={formData.budget_min}
                                                onChange={(e) => handleInputChange('budget_min', e.target.value)}
                                                placeholder="0.00"
                                                className={errors.budget_min ? 'border-red-500' : ''}
                                            />
                                            {errors.budget_min && <p className="text-sm text-red-500">{errors.budget_min}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="budget_max">Maximum Budget</Label>
                                            <Input
                                                id="budget_max"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={formData.budget_max}
                                                onChange={(e) => handleInputChange('budget_max', e.target.value)}
                                                placeholder="Optional"
                                                className={errors.budget_max ? 'border-red-500' : ''}
                                            />
                                            {errors.budget_max && <p className="text-sm text-red-500">{errors.budget_max}</p>}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Deadline */}
                            <div className="space-y-2">
                                <Label htmlFor="deadline">Deadline (Optional)</Label>
                                <Input
                                    id="deadline"
                                    type="date"
                                    value={formData.deadline}
                                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                                />
                            </div>

                            {/* Current Files */}
                            {project.files && project.files.length > 0 && (
                                <div className="space-y-2">
                                    <Label>Current Files</Label>
                                    <div className="space-y-2">
                                        {project.files.map((file) => (
                                            <div key={file.id} className="flex items-center gap-3 rounded-lg border p-3">
                                                <FileText className="h-4 w-4 text-muted-foreground" />
                                                <span className="flex-1 truncate text-sm">{file.original_name}</span>
                                                <span className="text-xs text-muted-foreground">{(file.file_size / 1024).toFixed(1)} KB</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* File Upload */}
                            <div className="space-y-2">
                                <Label htmlFor="files">Add New Files (Optional)</Label>
                                <div className="rounded-lg border-2 border-dashed border-muted-foreground/25 p-6">
                                    <div className="text-center">
                                        <Upload className="mx-auto h-8 w-8 text-muted-foreground/50" />
                                        <div className="mt-2">
                                            <Label htmlFor="files" className="cursor-pointer text-primary hover:underline">
                                                Click to upload files
                                            </Label>
                                            <Input
                                                id="files"
                                                type="file"
                                                multiple
                                                accept=".pdf,.doc,.docx"
                                                onChange={handleFileChange}
                                                className="hidden"
                                            />
                                        </div>
                                        <p className="mt-1 text-xs text-muted-foreground">PDF, DOC, DOCX up to 10MB each (max 5 files)</p>
                                    </div>
                                </div>

                                {/* Selected Files */}
                                {formData.files.length > 0 && (
                                    <div className="space-y-2">
                                        <Label>Selected Files</Label>
                                        <div className="space-y-2">
                                            {formData.files.map((file, index) => (
                                                <div key={index} className="flex items-center gap-3 rounded-lg border p-3">
                                                    <FileText className="h-4 w-4 text-muted-foreground" />
                                                    <span className="flex-1 truncate text-sm">{file.name}</span>
                                                    <span className="text-xs text-muted-foreground">{(file.size / 1024).toFixed(1)} KB</span>
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => removeFile(index)}
                                                        className="h-6 w-6 p-0"
                                                    >
                                                        <X className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Submit Buttons */}
                            <div className="flex gap-4 pt-4">
                                <Button type="submit" disabled={processing} className="flex-1">
                                    {processing ? 'Updating...' : 'Update Project'}
                                </Button>
                                <Button type="button" variant="outline" onClick={() => router.get(`/projects/${project.id}`)} className="flex-1">
                                    Cancel
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
