import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import { DollarSign, History, Minus, Plus, TrendingUp, Wallet } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    wallet_balance: number;
}

interface Props {
    user: User;
    balance: number;
}

const breadcrumbs = [
    { title: 'Wallet', href: '/wallet/balance' },
    { title: 'Balance', href: '/wallet/balance' },
];

export default function Balance({ user, balance }: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Wallet Balance" />

            <div className="mx-auto max-w-4xl space-y-6">
                {/* Header */}
                <div className="space-y-2">
                    <h1 className="text-3xl font-bold">Wallet</h1>
                    <p className="text-muted-foreground">Manage your funds, add money, and track your transactions</p>
                </div>

                {/* Balance Card */}
                <Card className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
                    <CardContent className="p-8">
                        <div className="flex items-center justify-between">
                            <div className="space-y-2">
                                <p className="text-primary-foreground/80">Available Balance</p>
                                <p className="text-4xl font-bold">₵{Number(balance || 0).toFixed(2)}</p>
                                <p className="text-sm text-primary-foreground/80">Last updated: {new Date().toLocaleDateString()}</p>
                            </div>
                            <div className="rounded-full bg-primary-foreground/10 p-4">
                                <Wallet className="h-8 w-8" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Quick Actions */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card className="transition-shadow hover:shadow-md">
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2 text-lg">
                                <Plus className="h-5 w-5 text-green-600" />
                                Add Funds
                            </CardTitle>
                            <CardDescription>Add money to your wallet using Paystack</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button asChild className="w-full">
                                <Link href="/wallet/add-funds">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Funds
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card className="transition-shadow hover:shadow-md">
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2 text-lg">
                                <Minus className="h-5 w-5 text-orange-600" />
                                Withdraw
                            </CardTitle>
                            <CardDescription>Transfer money to your bank account</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/wallet/withdraw">
                                    <Minus className="mr-2 h-4 w-4" />
                                    Withdraw
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card className="transition-shadow hover:shadow-md">
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center gap-2 text-lg">
                                <History className="h-5 w-5 text-blue-600" />
                                Transactions
                            </CardTitle>
                            <CardDescription>View your transaction history</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button asChild variant="outline" className="w-full">
                                <Link href={route('wallet.transactions')}>
                                    <History className="mr-2 h-4 w-4" />
                                    View History
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Wallet Info */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5" />
                                How it Works
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-3">
                                <div className="flex items-start gap-3">
                                    <div className="rounded-full bg-primary/10 p-1">
                                        <Plus className="h-4 w-4 text-primary" />
                                    </div>
                                    <div>
                                        <p className="font-medium">Add Funds</p>
                                        <p className="text-sm text-muted-foreground">Use Paystack to securely add money to your wallet</p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <div className="rounded-full bg-primary/10 p-1">
                                        <TrendingUp className="h-4 w-4 text-primary" />
                                    </div>
                                    <div>
                                        <p className="font-medium">Use for Projects</p>
                                        <p className="text-sm text-muted-foreground">Bid on projects and make secure payments</p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <div className="rounded-full bg-primary/10 p-1">
                                        <Minus className="h-4 w-4 text-primary" />
                                    </div>
                                    <div>
                                        <p className="font-medium">Withdraw Anytime</p>
                                        <p className="text-sm text-muted-foreground">Transfer unused funds back to your bank</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Security & Safety</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-3">
                                <div className="flex items-start gap-3">
                                    <div className="rounded-full bg-green-100 p-1">
                                        <div className="h-4 w-4 rounded-full bg-green-600"></div>
                                    </div>
                                    <div>
                                        <p className="font-medium">Secure Payments</p>
                                        <p className="text-sm text-muted-foreground">All transactions are encrypted and secure</p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <div className="rounded-full bg-green-100 p-1">
                                        <div className="h-4 w-4 rounded-full bg-green-600"></div>
                                    </div>
                                    <div>
                                        <p className="font-medium">Escrow Protection</p>
                                        <p className="text-sm text-muted-foreground">Project payments are held safely until completion</p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <div className="rounded-full bg-green-100 p-1">
                                        <div className="h-4 w-4 rounded-full bg-green-600"></div>
                                    </div>
                                    <div>
                                        <p className="font-medium">24/7 Support</p>
                                        <p className="text-sm text-muted-foreground">Get help with any wallet-related issues</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
