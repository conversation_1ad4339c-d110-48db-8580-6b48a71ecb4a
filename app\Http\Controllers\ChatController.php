<?php

namespace App\Http\Controllers;

use App\Models\Conversation;
use App\Models\Message;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ChatController extends Controller
{
    public function inbox()
    {
        $conversations = Conversation::with(['project', 'client', 'freelancer', 'latestMessage'])
            ->where(function ($query) {
                $query->where('client_id', Auth::id())
                    ->orWhere('freelancer_id', Auth::id());
            })
            ->orderBy('last_message_at', 'desc')
            ->get();

        $formattedConversations = $conversations->map(function ($conversation) {
            $otherUser = $conversation->client_id === Auth::id()
                ? $conversation->freelancer
                : $conversation->client;

            return [
                'id' => $conversation->id,
                'project' => [
                    'id' => $conversation->project->id,
                    'title' => $conversation->project->title,
                ],
                'other_user' => [
                    'id' => $otherUser->id,
                    'name' => $otherUser->name,
                    'email' => $otherUser->email,
                ],
                'last_message' => $conversation->latestMessage->first()?->message,
                'last_message_at' => $conversation->last_message_at,
                'unread_count' => $conversation->messages()
                    ->where('sender_id', '!=', Auth::id())
                    ->whereNull('read_at')
                    ->count(),
            ];
        });

        return Inertia::render('Inbox', [
            'conversations' => $formattedConversations,
        ]);
    }

    public function index()
    {
        $conversations = Conversation::with(['project', 'client', 'freelancer', 'latestMessage'])
            ->where(function ($query) {
                $query->where('client_id', Auth::id())
                    ->orWhere('freelancer_id', Auth::id());
            })
            ->orderBy('last_message_at', 'desc')
            ->get();

        return response()->json([
            'conversations' => $conversations->map(function ($conversation) {
                $otherUser = $conversation->client_id === Auth::id()
                    ? $conversation->freelancer
                    : $conversation->client;

                return [
                    'id' => $conversation->id,
                    'project' => [
                        'id' => $conversation->project->id,
                        'title' => $conversation->project->title,
                    ],
                    'other_user' => [
                        'id' => $otherUser->id,
                        'name' => $otherUser->name,
                    ],
                    'last_message' => $conversation->latestMessage->first()?->message,
                    'last_message_at' => $conversation->last_message_at,
                    'unread_count' => $conversation->messages()
                        ->where('sender_id', '!=', Auth::id())
                        ->whereNull('read_at')
                        ->count(),
                ];
            }),
        ]);
    }

    public function show(Conversation $conversation)
    {
        // Ensure user is part of this conversation
        if ($conversation->client_id !== Auth::id() && $conversation->freelancer_id !== Auth::id()) {
            abort(403);
        }

        $messages = $conversation->messages()
            ->with('sender')
            ->orderBy('created_at')
            ->get();

        // Mark messages as read
        $conversation->messages()
            ->where('sender_id', '!=', Auth::id())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return response()->json([
            'conversation' => [
                'id' => $conversation->id,
                'project' => [
                    'id' => $conversation->project->id,
                    'title' => $conversation->project->title,
                ],
                'other_user' => [
                    'id' => $conversation->client_id === Auth::id()
                        ? $conversation->freelancer->id
                        : $conversation->client->id,
                    'name' => $conversation->client_id === Auth::id()
                        ? $conversation->freelancer->name
                        : $conversation->client->name,
                ],
            ],
            'messages' => $messages->map(function ($message) {
                return [
                    'id' => $message->id,
                    'message' => $message->message,
                    'sender' => [
                        'id' => $message->sender->id,
                        'name' => $message->sender->name,
                    ],
                    'created_at' => $message->created_at->toISOString(),
                    'is_own' => $message->sender_id === Auth::id(),
                ];
            }),
        ]);
    }

    public function store(Request $request, Conversation $conversation)
    {
        // Ensure user is part of this conversation
        if ($conversation->client_id !== Auth::id() && $conversation->freelancer_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => Auth::id(),
            'message' => $request->message,
        ]);

        // Update conversation's last message timestamp
        $conversation->update(['last_message_at' => now()]);

        return response()->json([
            'message' => [
                'id' => $message->id,
                'message' => $message->message,
                'sender' => [
                    'id' => $message->sender->id,
                    'name' => $message->sender->name,
                ],
                'created_at' => $message->created_at->toISOString(),
                'is_own' => true,
            ],
        ]);
    }

    public function createOrGet(Request $request, Project $project)
    {
        $request->validate([
            'freelancer_id' => 'required|exists:users,id',
        ]);

        // Ensure the project belongs to the authenticated user (client)
        if ($project->user_id !== Auth::id()) {
            abort(403, 'You can only create conversations for your own projects.');
        }

        $conversation = Conversation::firstOrCreate([
            'project_id' => $project->id,
            'client_id' => Auth::id(),
            'freelancer_id' => $request->freelancer_id,
        ]);

        return response()->json([
            'conversation_id' => $conversation->id,
        ]);
    }
}
