import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Award, BookOpen, Check, Clock, Crown, Globe, Headphones, Shield, Star, Users, Zap } from 'lucide-react';

interface PricingPlan {
    id: string;
    title: string;
    subtitle: string;
    price: string;
    period: string;
    description: string;
    features: string[];
    cta: string;
    popular?: boolean;
    icon: React.ComponentType<any>;
    gradientFrom: string;
    gradientTo: string;
}

// Student Plans
const studentPlans: PricingPlan[] = [
    {
        id: 'student-basic',
        title: 'Student Basic',
        subtitle: 'Perfect for Individual Projects',
        price: 'GHS 0',
        period: 'month',
        description: 'Start your academic journey with essential project posting features.',
        features: [
            '3 Active Projects',
            'Basic Project Categories',
            'Email Support',
            'Standard Freelancer Pool',
            'Basic Messaging',
            'Project Templates',
        ],
        cta: 'Start Free',
        icon: BookOpen,
        gradientFrom: 'from-blue-500',
        gradientTo: 'to-cyan-500',
    },
    {
        id: 'student-plus',
        title: 'Student Plus',
        subtitle: 'Enhanced Academic Support',
        price: 'GHS 25',
        period: 'month',
        description: 'Get more features and priority support for your academic projects.',
        features: [
            '10 Active Projects',
            'All Project Categories',
            'Priority Email Support',
            'Verified Freelancer Access',
            'Advanced Messaging',
            'Project Templates & Guidelines',
            'Plagiarism Check Tools',
            'Progress Tracking',
        ],
        cta: 'Choose Plus',
        popular: true,
        icon: Star,
        gradientFrom: 'from-purple-500',
        gradientTo: 'to-pink-500',
    },
    {
        id: 'student-premium',
        title: 'Student Premium',
        subtitle: 'Complete Academic Solution',
        price: 'GHS 50',
        period: 'month',
        description: 'Everything you need for comprehensive academic success.',
        features: [
            'Unlimited Active Projects',
            'All Project Categories',
            '24/7 Priority Support',
            'Premium Freelancer Pool',
            'Advanced Communication Tools',
            'Custom Project Requirements',
            'Advanced Plagiarism Detection',
            'Detailed Progress Analytics',
            'Revision Management',
            'Academic Writing Resources',
        ],
        cta: 'Go Premium',
        icon: Crown,
        gradientFrom: 'from-amber-500',
        gradientTo: 'to-orange-500',
    },
];

// Freelancer Plans
const freelancerPlans: PricingPlan[] = [
    {
        id: 'freelancer-starter',
        title: 'Freelancer Starter',
        subtitle: 'Begin Your Journey',
        price: 'GHS 0',
        period: 'month',
        description: 'Start building your freelance career with basic access to projects.',
        features: [
            '10 Bids per Month',
            'Basic Profile Features',
            'Email Support',
            'Standard Project Access',
            'Basic Portfolio (3 items)',
            'Client Messaging',
        ],
        cta: 'Start Free',
        icon: Users,
        gradientFrom: 'from-green-500',
        gradientTo: 'to-emerald-500',
    },
    {
        id: 'freelancer-professional',
        title: 'Freelancer Pro',
        subtitle: 'Grow Your Business',
        price: 'GHS 40',
        period: 'month',
        description: 'Enhanced features to help you win more projects and build reputation.',
        features: [
            '50 Bids per Month',
            'Enhanced Profile Features',
            'Priority Support',
            'All Project Categories',
            'Extended Portfolio (10 items)',
            'Advanced Client Communication',
            'Bid Insights & Analytics',
            'Featured Profile Listing',
            'Skills Verification Badge',
        ],
        cta: 'Upgrade to Pro',
        popular: true,
        icon: Zap,
        gradientFrom: 'from-indigo-500',
        gradientTo: 'to-blue-500',
    },
    {
        id: 'freelancer-expert',
        title: 'Freelancer Expert',
        subtitle: 'Dominate Your Field',
        price: 'GHS 80',
        period: 'month',
        description: 'Maximum exposure and tools for established freelancers.',
        features: [
            'Unlimited Bids',
            'Premium Profile Features',
            '24/7 Priority Support',
            'Exclusive High-Value Projects',
            'Unlimited Portfolio Items',
            'VIP Client Communication',
            'Advanced Analytics Dashboard',
            'Top Freelancer Badge',
            'Custom Skills Certification',
            'Direct Client Invitations',
            'Revenue Optimization Tools',
        ],
        cta: 'Become Expert',
        icon: Award,
        gradientFrom: 'from-purple-600',
        gradientTo: 'to-indigo-600',
    },
];

export default function Membership() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="Membership Plans">
                <meta name="description" content="Choose the perfect membership plan for your academic journey or freelance career on Thesylink." />
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
                {/* Navigation */}
                <header className="relative z-10 w-full">
                    <nav className="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8">
                        <Link href="/" className="flex items-center gap-2">
                            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-white">
                                <BookOpen className="h-5 w-5" />
                            </div>
                            <span className="text-xl font-semibold text-gray-900 dark:text-white">Thesylink</span>
                        </Link>
                        <div className="flex items-center gap-4">
                            {auth.user ? (
                                <Link
                                    href={route('dashboard')}
                                    className="rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/90"
                                >
                                    Dashboard
                                </Link>
                            ) : (
                                <>
                                    <Link
                                        href={route('login')}
                                        className="text-sm font-medium text-gray-700 transition-colors hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                                    >
                                        Log in
                                    </Link>
                                    <Link
                                        href={route('register')}
                                        className="rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/90"
                                    >
                                        Get Started
                                    </Link>
                                </>
                            )}
                        </div>
                    </nav>
                </header>

                {/* Hero Section */}
                <section className="relative px-6 lg:px-8">
                    <div className="mx-auto max-w-4xl py-16 sm:py-24">
                        <div className="text-center">
                            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl dark:text-white">
                                Choose Your
                                <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"> Perfect Plan</span>
                            </h1>
                            <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
                                Whether you're a student seeking academic support or a freelancer ready to showcase your expertise, we have the
                                perfect membership plan to accelerate your success.
                            </p>
                        </div>
                    </div>
                </section>

                {/* Student Plans Section */}
                <section className="px-6 py-16 lg:px-8">
                    <div className="mx-auto max-w-7xl">
                        <div className="mb-16 text-center">
                            <div className="mb-4 flex items-center justify-center gap-2">
                                <BookOpen className="h-8 w-8 text-primary" />
                                <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Student Plans</h2>
                            </div>
                            <p className="text-lg text-gray-600 dark:text-gray-300">Get the academic support you need to excel in your studies</p>
                        </div>

                        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                            {studentPlans.map((plan) => (
                                <PricingCard key={plan.id} plan={plan} />
                            ))}
                        </div>
                    </div>
                </section>

                {/* Freelancer Plans Section */}
                <section className="bg-white/50 px-6 py-16 lg:px-8 dark:bg-slate-800/50">
                    <div className="mx-auto max-w-7xl">
                        <div className="mb-16 text-center">
                            <div className="mb-4 flex items-center justify-center gap-2">
                                <Users className="h-8 w-8 text-secondary" />
                                <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Freelancer Plans</h2>
                            </div>
                            <p className="text-lg text-gray-600 dark:text-gray-300">
                                Build your freelance career and connect with academic opportunities
                            </p>
                        </div>

                        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                            {freelancerPlans.map((plan) => (
                                <PricingCard key={plan.id} plan={plan} />
                            ))}
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="px-6 py-16 lg:px-8">
                    <div className="mx-auto max-w-7xl">
                        <div className="mb-16 text-center">
                            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Why Choose Thesylink?</h2>
                            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                                Discover the benefits that make us the premier academic freelancing platform
                            </p>
                        </div>

                        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
                            <FeatureCard
                                icon={Shield}
                                title="Secure Payments"
                                description="Escrow protection ensures safe transactions for both students and freelancers"
                                color="text-green-600"
                            />
                            <FeatureCard
                                icon={Globe}
                                title="Global Network"
                                description="Connect with academic experts from universities around the world"
                                color="text-blue-600"
                            />
                            <FeatureCard
                                icon={Clock}
                                title="24/7 Support"
                                description="Get help whenever you need it with our dedicated support team"
                                color="text-purple-600"
                            />
                            <FeatureCard
                                icon="Headphones"
                                title="Quality Assurance"
                                description="Every project goes through our quality review process"
                                color="text-orange-600"
                            />
                        </div>
                    </div>
                </section>

                {/* FAQ Section */}
                <section className="bg-white/50 px-6 py-16 lg:px-8 dark:bg-slate-800/50">
                    <div className="mx-auto max-w-3xl">
                        <div className="mb-16 text-center">
                            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Frequently Asked Questions</h2>
                        </div>

                        <div className="space-y-8">
                            <div className="border-b border-gray-200 pb-6 dark:border-gray-700">
                                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">Can I change my plan anytime?</h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Yes! You can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                                </p>
                            </div>
                            <div className="border-b border-gray-200 pb-6 dark:border-gray-700">
                                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">Do you offer refunds?</h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact our support team.
                                </p>
                            </div>
                            <div className="border-b border-gray-200 pb-6 dark:border-gray-700">
                                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">Are there any hidden fees?</h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    No hidden fees! The prices shown include all features. We only charge a small transaction fee on completed
                                    projects.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="bg-primary py-16 sm:py-24">
                    <div className="mx-auto max-w-7xl px-6 lg:px-8">
                        <div className="mx-auto max-w-2xl text-center">
                            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">Ready to Get Started?</h2>
                            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-blue-100">
                                Join thousands of students and freelancers who trust Thesylink for their academic success.
                            </p>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <Link
                                    href={route('register')}
                                    className="rounded-lg bg-white px-6 py-3 text-sm font-semibold text-primary shadow-sm transition-all hover:bg-gray-50 hover:shadow-lg"
                                >
                                    Start Your Journey
                                </Link>
                                <Link
                                    href={route('login')}
                                    className="rounded-lg border border-white px-6 py-3 text-sm font-semibold text-white transition-all hover:bg-white hover:text-primary"
                                >
                                    Sign In
                                </Link>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-white dark:bg-gray-900">
                    <div className="mx-auto max-w-7xl px-6 py-12 lg:px-8">
                        <div className="flex items-center justify-center">
                            <Link href="/" className="flex items-center gap-2">
                                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-white">
                                    <BookOpen className="h-5 w-5" />
                                </div>
                                <span className="text-xl font-semibold text-gray-900 dark:text-white">Thesylink</span>
                            </Link>
                        </div>
                        <p className="mt-4 text-center text-sm leading-6 text-gray-600 dark:text-gray-400">
                            © 2025 Thesylink. Connecting students with academic excellence.
                        </p>
                    </div>
                </footer>
            </div>
        </>
    );
}

// Pricing Card Component
function PricingCard({ plan }: { plan: PricingPlan }) {
    const IconComponent = plan.icon;

    return (
        <Card className={`relative p-6 ${plan.popular ? 'scale-105 shadow-xl ring-2 ring-primary' : ''} transition-all hover:shadow-lg`}>
            {plan.popular && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2 transform">
                    <Badge className="bg-primary px-4 py-1 text-white">Most Popular</Badge>
                </div>
            )}

            <div className="text-center">
                <div
                    className={`mx-auto h-16 w-16 rounded-full bg-gradient-to-r ${plan.gradientFrom} ${plan.gradientTo} mb-4 flex items-center justify-center`}
                >
                    <IconComponent className="h-8 w-8 text-white" />
                </div>

                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{plan.title}</h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">{plan.subtitle}</p>

                <div className="mt-4 mb-2">
                    <span className="text-3xl font-bold text-gray-900 dark:text-white">{plan.price}</span>
                    <span className="text-gray-600 dark:text-gray-400">/{plan.period}</span>
                </div>

                <p className="mb-6 text-sm text-gray-600 dark:text-gray-300">{plan.description}</p>
            </div>

            <ul className="mb-6 space-y-3">
                {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                        <Check className="mr-3 h-4 w-4 flex-shrink-0 text-green-500" />
                        <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                    </li>
                ))}
            </ul>

            <div className="mt-auto">
                <Button
                    asChild
                    className={`w-full ${plan.popular ? 'bg-primary hover:bg-primary/90' : 'bg-gray-900 hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100'}`}
                >
                    <Link href={route('register')}>{plan.cta}</Link>
                </Button>
            </div>
        </Card>
    );
}

// Feature Card Component
function FeatureCard({
    icon: Icon,
    title,
    description,
    color,
}: {
    icon: React.ComponentType<any> | string;
    title: string;
    description: string;
    color: string;
}) {
    const IconComponent = typeof Icon === 'string' ? (Icon === 'Headphones' ? Headphones : Shield) : Icon;

    return (
        <div className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800">
                <IconComponent className={`h-6 w-6 ${color}`} />
            </div>
            <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
            <p className="text-gray-600 dark:text-gray-300">{description}</p>
        </div>
    );
}
