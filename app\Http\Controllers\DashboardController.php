<?php

namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $userId = Auth::id();
        $projects = Project::with(['files'])
            ->where('user_id', $userId)
            ->latest()
            ->get();

        return Inertia::render('dashboard', [
            'projects' => $projects->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'slug' => $project->slug,
                    'description' => $project->description,
                    'budget_min' => $project->budget_min ? (float) $project->budget_min : null,
                    'budget_max' => $project->budget_max ? (float) $project->budget_max : null,
                    'budget_type' => $project->budget_type,
                    'deadline' => $project->deadline?->format('Y-m-d'),
                    'category' => $project->category,
                    'academic_level' => $project->academic_level,
                    'status' => $project->status,
                    'file_count' => $project->file_count,
                    'created_at' => $project->created_at->toISOString(),
                ];
            }),
        ]);
    }

    public function myProjects()
    {
        $userId = Auth::id();

        // Get user's projects with detailed information
        $projects = Project::with(['files', 'bids', 'assignedFreelancer', 'milestones'])
            ->where('user_id', $userId)
            ->latest()
            ->get();

        // Get projects where user is the assigned freelancer
        $assignedProjects = Project::with(['files', 'user', 'milestones'])
            ->where('assigned_freelancer_id', $userId)
            ->latest()
            ->get();

        // Get user's bids on other projects
        $myBids = \App\Models\Bid::with(['project.user'])
            ->where('user_id', $userId)
            ->latest()
            ->get();

        // Calculate statistics
        $stats = [
            'total_projects_posted' => $projects->count(),
            'active_projects_posted' => $projects->where('status', 'open')->count(),
            'completed_projects_posted' => $projects->where('status', 'completed')->count(),
            'total_projects_working' => $assignedProjects->count(),
            'active_projects_working' => $assignedProjects->where('status', 'in_progress')->count(),
            'completed_projects_working' => $assignedProjects->where('status', 'completed')->count(),
            'total_bids_submitted' => $myBids->count(),
            'pending_bids' => $myBids->where('status', 'pending')->count(),
            'accepted_bids' => $myBids->where('status', 'accepted')->count(),
            'total_spent' => $projects->where('status', 'completed')->sum('accepted_bid_amount'),
            'total_earned' => $assignedProjects->where('status', 'completed')->sum('accepted_bid_amount'),
        ];

        return Inertia::render('MyProjects', [
            'postedProjects' => $projects->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'slug' => $project->slug,
                    'description' => $project->description,
                    'budget_min' => $project->budget_min ? (float) $project->budget_min : null,
                    'budget_max' => $project->budget_max ? (float) $project->budget_max : null,
                    'budget_type' => $project->budget_type,
                    'deadline' => $project->deadline?->format('Y-m-d'),
                    'category' => $project->category,
                    'academic_level' => $project->academic_level,
                    'status' => $project->status,
                    'file_count' => $project->file_count,
                    'created_at' => $project->created_at->toISOString(),
                    'bids_count' => $project->bids->count(),
                    'assigned_freelancer' => $project->assignedFreelancer ? [
                        'id' => $project->assignedFreelancer->id,
                        'name' => $project->assignedFreelancer->name,
                    ] : null,
                    'accepted_bid_amount' => $project->accepted_bid_amount ? (float) $project->accepted_bid_amount : null,
                    'milestones_progress' => $project->milestones->count() > 0 ?
                        ($project->milestones->where('status', 'approved')->count() / $project->milestones->count()) * 100 : 0,
                ];
            }),
            'assignedProjects' => $assignedProjects->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'slug' => $project->slug,
                    'description' => $project->description,
                    'status' => $project->status,
                    'accepted_bid_amount' => $project->accepted_bid_amount ? (float) $project->accepted_bid_amount : null,
                    'deadline' => $project->deadline?->format('Y-m-d'),
                    'created_at' => $project->created_at->toISOString(),
                    'client' => [
                        'id' => $project->user->id,
                        'name' => $project->user->name,
                    ],
                    'milestones_progress' => $project->milestones->count() > 0 ?
                        ($project->milestones->where('status', 'approved')->count() / $project->milestones->count()) * 100 : 0,
                ];
            }),
            'myBids' => $myBids->map(function ($bid) {
                return [
                    'id' => $bid->id,
                    'amount' => (float) $bid->amount,
                    'proposal' => $bid->proposal,
                    'delivery_days' => $bid->delivery_days,
                    'status' => $bid->status,
                    'created_at' => $bid->created_at->toISOString(),
                    'project' => [
                        'id' => $bid->project->id,
                        'title' => $bid->project->title,
                        'slug' => $bid->project->slug,
                        'status' => $bid->project->status,
                        'client' => [
                            'id' => $bid->project->user->id,
                            'name' => $bid->project->user->name,
                        ],
                    ],
                ];
            }),
            'stats' => $stats,
        ]);
    }
}
