import { Breadcrumbs } from '@/components/breadcrumbs';
import { Icon } from '@/components/icon';
import { MessagesDropdown } from '@/components/messages-dropdown';
import { NotificationsDropdown } from '@/components/notifications-dropdown';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList, navigationMenuTriggerStyle } from '@/components/ui/navigation-menu';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { UserMenuContent } from '@/components/user-menu-content';
import { useInitials } from '@/hooks/use-initials';
import { cn } from '@/lib/utils';
import { type BreadcrumbItem, type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { Bell, Folder, LayoutGrid, Mail, Menu, MessageCircle, Plus, Search } from 'lucide-react';
import { useEffect, useState } from 'react';
import AppLogo from './app-logo';
import AppLogoIcon from './app-logo-icon';

const mainNavItems: NavItem[] = [
    // Dashboard moved to secondary header
];

const rightNavItems: NavItem[] = [
    {
        title: 'Notifications',
        href: '/notifications',
        icon: Bell,
    },
    {
        title: 'Recent Messages',
        href: '/messages',
        icon: MessageCircle,
    },
];

const activeItemStyles = 'text-neutral-900 dark:bg-neutral-800 dark:text-neutral-100';

interface AppHeaderProps {
    breadcrumbs?: BreadcrumbItem[];
}

export function AppHeader({ breadcrumbs = [] }: AppHeaderProps) {
    const page = usePage<SharedData>();
    const { auth } = page.props;
    const getInitials = useInitials();
    const [unreadMessagesCount, setUnreadMessagesCount] = useState(0);

    // TODO: Implement real API call when message system has unread count endpoint
    const fetchUnreadMessageCount = async () => {
        try {
            // Placeholder for future API implementation
            // const response = await fetch('/api/conversations/unread-count');
            // const data = await response.json();
            // setUnreadMessagesCount(data.count);

            // For now, set to 0 since no users have new chats yet
            setUnreadMessagesCount(0);
        } catch (error) {
            console.error('Failed to fetch unread message count:', error);
            setUnreadMessagesCount(0);
        }
    };

    useEffect(() => {
        fetchUnreadMessageCount();
    }, []);
    return (
        <>
            <div className="border-b border-sidebar-border/80 bg-black text-white">
                <div className="mx-auto flex h-16 items-center px-4 md:max-w-7xl">
                    {/* Mobile Menu */}
                    <div className="lg:hidden">
                        <Sheet>
                            <SheetTrigger asChild>
                                <Button variant="ghost" size="icon" className="mr-2 h-[34px] w-[34px] text-white hover:bg-white/10">
                                    <Menu className="h-5 w-5" />
                                </Button>
                            </SheetTrigger>
                            <SheetContent side="left" className="flex h-full w-64 flex-col items-stretch justify-between bg-sidebar">
                                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                                <SheetHeader className="flex justify-start text-left">
                                    <AppLogoIcon className="h-6 w-6 fill-current text-black dark:text-white" />
                                </SheetHeader>
                                <div className="flex h-full flex-1 flex-col space-y-4 p-4">
                                    <div className="flex h-full flex-col justify-between text-sm">
                                        <div className="flex flex-col space-y-4">
                                            {/* Secondary nav items for mobile */}
                                            <Link href="/dashboard" className="flex items-center space-x-2 font-medium">
                                                <Icon iconNode={LayoutGrid} className="h-5 w-5" />
                                                <span>Dashboard</span>
                                            </Link>
                                            <Link href="/my-projects" className="flex items-center space-x-2 font-medium">
                                                <Icon iconNode={Folder} className="h-5 w-5" />
                                                <span>My Projects</span>
                                            </Link>
                                            <Link href="/browse" className="flex items-center space-x-2 font-medium">
                                                <Icon iconNode={Search} className="h-5 w-5" />
                                                <span>Browse Projects</span>
                                            </Link>
                                            <Link href="/inbox" className="flex items-center space-x-2 font-medium">
                                                <Icon iconNode={Mail} className="h-5 w-5" />
                                                <span>Inbox</span>
                                                {unreadMessagesCount > 0 && (
                                                    <Badge className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-destructive p-0 text-xs text-destructive-foreground">
                                                        {unreadMessagesCount > 99 ? '99+' : unreadMessagesCount}
                                                    </Badge>
                                                )}
                                            </Link>
                                            {mainNavItems.map((item) => (
                                                <Link key={item.title} href={item.href} className="flex items-center space-x-2 font-medium">
                                                    {item.icon && <Icon iconNode={item.icon} className="h-5 w-5" />}
                                                    <span>{item.title}</span>
                                                </Link>
                                            ))}
                                        </div>

                                        <div className="flex flex-col space-y-4">
                                            <Link href="/notifications" className="flex items-center space-x-2 font-medium">
                                                <Icon iconNode={Bell} className="h-5 w-5" />
                                                <span>Notifications</span>
                                            </Link>
                                            <Link href="/messages" className="flex items-center space-x-2 font-medium">
                                                <Icon iconNode={MessageCircle} className="h-5 w-5" />
                                                <span>Recent Messages</span>
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </SheetContent>
                        </Sheet>
                    </div>

                    <div className="flex items-center space-x-4 md:space-x-6">
                        <Link href="/dashboard" prefetch className="flex items-center space-x-2">
                            <AppLogo />
                        </Link>

                        <nav className="hidden items-center space-x-6 md:flex">
                            <Link href="/browse" className="text-sm font-medium text-white transition-colors hover:text-white/80">
                                Browse Projects
                            </Link>
                        </nav>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="ml-6 hidden h-full items-center space-x-6 lg:flex">
                        <NavigationMenu className="flex h-full items-stretch">
                            <NavigationMenuList className="flex h-full items-stretch space-x-2">
                                {mainNavItems.map((item, index) => (
                                    <NavigationMenuItem key={index} className="relative flex h-full items-center">
                                        <Link
                                            href={item.href}
                                            className={cn(
                                                navigationMenuTriggerStyle(),
                                                page.url === item.href && activeItemStyles,
                                                'h-9 cursor-pointer px-3',
                                            )}
                                        >
                                            {item.icon && <Icon iconNode={item.icon} className="mr-2 h-4 w-4" />}
                                            {item.title}
                                        </Link>
                                        {page.url === item.href && (
                                            <div className="absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-black dark:bg-white"></div>
                                        )}
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    <div className="ml-auto flex items-center space-x-2">
                        <div className="hidden items-center space-x-1 lg:flex">
                            <NotificationsDropdown />
                            <MessagesDropdown />
                        </div>

                        {/* Post a Project Button */}
                        <Button asChild className="bg-primary text-primary-foreground hover:bg-primary/90">
                            <Link href="/projects/create">
                                <Plus className="mr-1 h-4 w-4 sm:mr-2" />
                                <span className="hidden sm:inline">Post a Project</span>
                                <span className="sm:hidden">Post</span>
                            </Link>
                        </Button>

                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="relative z-50 size-10 rounded-full p-1 hover:bg-white/10">
                                    <Avatar className="size-8 overflow-hidden rounded-full">
                                        <AvatarImage src={auth.user.avatar} alt={auth.user.name} />
                                        <AvatarFallback className="rounded-lg bg-white/20 text-white">{getInitials(auth.user.name)}</AvatarFallback>
                                    </Avatar>
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-56" align="end">
                                <UserMenuContent user={auth.user} />
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>
            </div>
            {breadcrumbs.length > 1 && (
                <div className="flex w-full border-b border-white/20">
                    <div className="mx-auto flex h-12 w-full items-center justify-start px-4 text-white/70 md:max-w-7xl">
                        <Breadcrumbs breadcrumbs={breadcrumbs} />
                    </div>
                </div>
            )}
        </>
    );
}
