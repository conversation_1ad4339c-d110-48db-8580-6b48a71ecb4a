<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GoogleAuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_google_auth_redirect_works()
    {
        $response = $this->get('/auth/google');

        $response->assertRedirect();
        $this->assertStringContainsString('accounts.google.com', $response->headers->get('Location'));
    }

    public function test_users_can_create_account_with_google_id()
    {
        $user = User::factory()->create([
            'google_id' => '*********',
            'password' => null,
        ]);

        $this->assertDatabaseHas('users', [
            'google_id' => '*********',
            'password' => null,
        ]);
    }

    public function test_existing_user_can_be_linked_with_google()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'google_id' => null,
        ]);

        $user->update(['google_id' => '*********']);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'google_id' => '*********',
        ]);
    }
}
