import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Calendar, DollarSign, FileText, Target, TrendingUp } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Analytics', href: '/analytics' },
];

interface User {
    id: number;
    name: string;
    email: string;
}

interface MonthlyData {
    month: string;
    earnings: number;
    spending: number;
}

interface ProjectTimeline {
    month: string;
    total: number;
    completed: number;
}

interface BidTimeline {
    month: string;
    total_bids: number;
    accepted_bids: number;
}

interface Summary {
    total_earned: number;
    total_spent: number;
    net_balance: number;
    projects_posted: number;
    projects_completed: number;
    total_bids: number;
    accepted_bids: number;
}

interface Props {
    user: User;
    monthly_data: MonthlyData[];
    project_timeline: ProjectTimeline[];
    bid_timeline: BidTimeline[];
    summary: Summary;
}

export default function AnalyticsShow({ user, monthly_data, project_timeline, bid_timeline, summary }: Props) {
    const formatCurrency = (amount: number) => `₵${Number(amount || 0).toFixed(2)}`;

    const formatMonth = (monthStr: string) => {
        const date = new Date(monthStr + '-01');
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Account Analytics" />

            <div className="space-y-8">
                {/* Header */}
                <div>
                    <h1 className="text-2xl font-bold sm:text-3xl">Account Analytics</h1>
                    <p className="text-muted-foreground">Track your performance and financial metrics</p>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Total Earned</p>
                                    <p className="text-2xl font-bold">{formatCurrency(summary.total_earned)}</p>
                                    <p className="text-xs text-green-600">+{formatCurrency(summary.net_balance)} net</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Total Spent</p>
                                    <p className="text-2xl font-bold">{formatCurrency(summary.total_spent)}</p>
                                    <p className="text-xs text-muted-foreground">Project payments</p>
                                </div>
                                <TrendingUp className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Projects Posted</p>
                                    <p className="text-2xl font-bold">{summary.projects_posted}</p>
                                    <p className="text-xs text-green-600">
                                        {summary.projects_posted > 0 ? Math.round((summary.projects_completed / summary.projects_posted) * 100) : 0}%
                                        completed
                                    </p>
                                </div>
                                <FileText className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Bid Success Rate</p>
                                    <p className="text-2xl font-bold">
                                        {summary.total_bids > 0 ? Math.round((summary.accepted_bids / summary.total_bids) * 100) : 0}%
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                        {summary.accepted_bids}/{summary.total_bids} accepted
                                    </p>
                                </div>
                                <Target className="h-8 w-8 text-purple-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Monthly Financial Data */}
                <Card>
                    <CardHeader>
                        <CardTitle>Monthly Financial Overview</CardTitle>
                        <CardDescription>Track your earnings and spending over time</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {monthly_data.length > 0 ? (
                            <div className="space-y-4">
                                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                    {monthly_data.slice(0, 6).map((data) => (
                                        <div key={data.month} className="rounded-lg border p-4">
                                            <div className="mb-2 flex items-center justify-between">
                                                <h4 className="font-medium">{formatMonth(data.month)}</h4>
                                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                            </div>
                                            <div className="space-y-2">
                                                <div className="flex justify-between text-sm">
                                                    <span className="text-green-600">Earned:</span>
                                                    <span className="font-medium">{formatCurrency(Number(data.earnings))}</span>
                                                </div>
                                                <div className="flex justify-between text-sm">
                                                    <span className="text-red-600">Spent:</span>
                                                    <span className="font-medium">{formatCurrency(Number(data.spending))}</span>
                                                </div>
                                                <div className="flex justify-between border-t pt-2 text-sm">
                                                    <span className="font-medium">Net:</span>
                                                    <span
                                                        className={`font-medium ${
                                                            Number(data.earnings) - Number(data.spending) >= 0 ? 'text-green-600' : 'text-red-600'
                                                        }`}
                                                    >
                                                        {formatCurrency(Number(data.earnings) - Number(data.spending))}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ) : (
                            <p className="py-8 text-center text-muted-foreground">No financial data available yet</p>
                        )}
                    </CardContent>
                </Card>

                {/* Project Performance */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Project Timeline</CardTitle>
                            <CardDescription>Monthly project posting and completion rates</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {project_timeline.length > 0 ? (
                                <div className="space-y-4">
                                    {project_timeline.slice(0, 6).map((data) => (
                                        <div key={data.month} className="flex items-center justify-between border-b pb-2 last:border-b-0">
                                            <div>
                                                <p className="font-medium">{formatMonth(data.month)}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {data.completed}/{data.total} completed
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium">
                                                    {data.total > 0 ? Math.round((data.completed / data.total) * 100) : 0}%
                                                </p>
                                                <div className="mt-1 h-2 w-16 rounded-full bg-gray-200">
                                                    <div
                                                        className="h-2 rounded-full bg-blue-600"
                                                        style={{
                                                            width: `${data.total > 0 ? (data.completed / data.total) * 100 : 0}%`,
                                                        }}
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="py-8 text-center text-muted-foreground">No project data available yet</p>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Bid Performance</CardTitle>
                            <CardDescription>Monthly bid submission and acceptance rates</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {bid_timeline.length > 0 ? (
                                <div className="space-y-4">
                                    {bid_timeline.slice(0, 6).map((data) => (
                                        <div key={data.month} className="flex items-center justify-between border-b pb-2 last:border-b-0">
                                            <div>
                                                <p className="font-medium">{formatMonth(data.month)}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {data.accepted_bids}/{data.total_bids} accepted
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium">
                                                    {data.total_bids > 0 ? Math.round((data.accepted_bids / data.total_bids) * 100) : 0}%
                                                </p>
                                                <div className="mt-1 h-2 w-16 rounded-full bg-gray-200">
                                                    <div
                                                        className="h-2 rounded-full bg-green-600"
                                                        style={{
                                                            width: `${data.total_bids > 0 ? (data.accepted_bids / data.total_bids) * 100 : 0}%`,
                                                        }}
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="py-8 text-center text-muted-foreground">No bid data available yet</p>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Performance Insights */}
                <Card>
                    <CardHeader>
                        <CardTitle>Performance Insights</CardTitle>
                        <CardDescription>Key metrics and recommendations for improvement</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div className="rounded-lg border p-4">
                                <h4 className="mb-2 font-medium">As a Client</h4>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span>Projects posted:</span>
                                        <span className="font-medium">{summary.projects_posted}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Completion rate:</span>
                                        <span className="font-medium">
                                            {summary.projects_posted > 0
                                                ? Math.round((summary.projects_completed / summary.projects_posted) * 100)
                                                : 0}
                                            %
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Total spent:</span>
                                        <span className="font-medium">{formatCurrency(summary.total_spent)}</span>
                                    </div>
                                </div>
                            </div>

                            <div className="rounded-lg border p-4">
                                <h4 className="mb-2 font-medium">As a Freelancer</h4>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span>Bids submitted:</span>
                                        <span className="font-medium">{summary.total_bids}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Success rate:</span>
                                        <span className="font-medium">
                                            {summary.total_bids > 0 ? Math.round((summary.accepted_bids / summary.total_bids) * 100) : 0}%
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Total earned:</span>
                                        <span className="font-medium">{formatCurrency(summary.total_earned)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
