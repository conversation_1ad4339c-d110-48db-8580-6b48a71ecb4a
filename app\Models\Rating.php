<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Rating extends Model
{
    protected $fillable = [
        'project_id',
        'client_id',
        'freelancer_id',
        'rated_by',
        'rated_for',
        'rating',
        'review',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    public function freelancer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'freelancer_id');
    }

    public function ratedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, $this->rated_by === 'client' ? 'client_id' : 'freelancer_id');
    }
}
