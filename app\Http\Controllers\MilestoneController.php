<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\ProjectMilestone;
use App\Services\EscrowService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class MilestoneController extends Controller
{
    protected $escrowService;

    public function __construct(EscrowService $escrowService)
    {
        $this->escrowService = $escrowService;
    }

    /**
     * Display milestones for a project
     */
    public function index(Project $project)
    {
        // Ensure user has access to view milestones
        if ($project->user_id !== Auth::id() && $project->assigned_freelancer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to project milestones.');
        }

        $project->load(['milestones', 'assignedFreelancer', 'user']);

        return Inertia::render('projects/milestones', [
            'project' => $project,
            'isFreelancer' => Auth::id() === $project->assigned_freelancer_id,
            'isClient' => Auth::id() === $project->user_id,
        ]);
    }

    /**
     * Start working on a milestone (freelancer)
     */
    public function start(Project $project, ProjectMilestone $milestone)
    {
        // Ensure freelancer is assigned to this project
        if ($project->assigned_freelancer_id !== Auth::id()) {
            return back()->withErrors(['error' => 'You are not assigned to this project.']);
        }

        // Ensure milestone belongs to this project
        if ($milestone->project_id !== $project->id) {
            return back()->withErrors(['error' => 'Invalid milestone for this project.']);
        }

        // Ensure milestone can be started
        if (! $milestone->canBeStarted()) {
            return back()->withErrors(['error' => 'This milestone cannot be started at this time.']);
        }

        try {
            $milestone->update([
                'status' => 'in_progress',
                'started_at' => now(),
            ]);

            return back()->with('success', 'Milestone started successfully. You can now work on this chapter.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to start milestone. Please try again.']);
        }
    }

    /**
     * Submit a milestone for review (freelancer)
     */
    public function submit(Request $request, Project $project, ProjectMilestone $milestone)
    {
        // Ensure freelancer is assigned to this project
        if ($project->assigned_freelancer_id !== Auth::id()) {
            return back()->withErrors(['error' => 'You are not assigned to this project.']);
        }

        // Ensure milestone belongs to this project
        if ($milestone->project_id !== $project->id) {
            return back()->withErrors(['error' => 'Invalid milestone for this project.']);
        }

        // Ensure milestone can be submitted
        if (! $milestone->canBeSubmitted()) {
            return back()->withErrors(['error' => 'This milestone cannot be submitted at this time.']);
        }

        $validated = $request->validate([
            'submission_notes' => 'required|string|max:1000',
        ]);

        try {
            $milestone->update([
                'status' => 'submitted',
                'submitted_at' => now(),
                'submission_notes' => $validated['submission_notes'],
            ]);

            return back()->with('success', 'Milestone submitted successfully. Waiting for client approval.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to submit milestone. Please try again.']);
        }
    }

    /**
     * Approve a milestone and release payment (client)
     */
    public function approve(Request $request, Project $project, ProjectMilestone $milestone)
    {
        // Ensure user owns the project
        if ($project->user_id !== Auth::id()) {
            return back()->withErrors(['error' => 'You can only approve milestones on your own projects.']);
        }

        // Ensure milestone belongs to this project
        if ($milestone->project_id !== $project->id) {
            return back()->withErrors(['error' => 'Invalid milestone for this project.']);
        }

        // Ensure milestone can be approved
        if (! $milestone->canBeApproved()) {
            return back()->withErrors(['error' => 'This milestone cannot be approved at this time.']);
        }

        $validated = $request->validate([
            'approval_notes' => 'nullable|string|max:1000',
        ]);

        try {
            // Update milestone status
            $milestone->update([
                'status' => 'approved',
                'approved_at' => now(),
                'approval_notes' => $validated['approval_notes'] ?? null,
            ]);

            // Release payment through escrow service
            $paymentReleased = $this->escrowService->releaseMilestonePayment($milestone);

            if (! $paymentReleased) {
                // Rollback milestone status if payment release failed
                $milestone->update(['status' => 'submitted']);

                return back()->withErrors(['error' => 'Failed to release payment. Please try again.']);
            }

            return back()->with('success', 'Milestone approved and payment released successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to approve milestone. Please try again.']);
        }
    }

    /**
     * Request revision for a milestone (client)
     */
    public function requestRevision(Request $request, Project $project, ProjectMilestone $milestone)
    {
        // Ensure user owns the project
        if ($project->user_id !== Auth::id()) {
            return back()->withErrors(['error' => 'You can only request revisions on your own projects.']);
        }

        // Ensure milestone belongs to this project
        if ($milestone->project_id !== $project->id) {
            return back()->withErrors(['error' => 'Invalid milestone for this project.']);
        }

        // Ensure milestone is submitted
        if ($milestone->status !== 'submitted') {
            return back()->withErrors(['error' => 'This milestone is not available for revision requests.']);
        }

        $validated = $request->validate([
            'revision_notes' => 'required|string|max:1000',
        ]);

        try {
            $milestone->update([
                'status' => 'in_progress',
                'approval_notes' => $validated['revision_notes'],
            ]);

            return back()->with('success', 'Revision requested successfully. The freelancer has been notified.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to request revision. Please try again.']);
        }
    }
}
