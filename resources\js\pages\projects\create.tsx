import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { FileText, Upload, X } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Post Project',
        href: '/projects/create',
    },
];

const categories = [
    'Computer Science',
    'Nursing',
    'Business Administration',
    'Engineering',
    'Psychology',
    'Education',
    'Medicine',
    'Law',
    'Mathematics',
    'Biology',
    'Chemistry',
    'Physics',
    'Literature',
    'History',
    'Other',
];

const academicLevels = ['High School', 'Bachelor', 'Master', 'PhD', 'Professional'];

const budgetTypes = [
    { value: 'fixed', label: 'Fixed Price' },
    { value: 'hourly', label: 'Hourly Rate' },
    { value: 'negotiable', label: 'Negotiable' },
];

interface FormData {
    title: string;
    description: string;
    requirements: string;
    budget_min: string;
    budget_max: string;
    budget_type: string;
    deadline: string;
    category: string;
    academic_level: string;
    files: File[];
}

export default function CreateProject() {
    const { toast } = useToast();
    const [formData, setFormData] = useState<FormData>({
        title: '',
        description: '',
        requirements: '',
        budget_min: '',
        budget_max: '',
        budget_type: 'fixed',
        deadline: '',
        category: '',
        academic_level: '',
        files: [],
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [processing, setProcessing] = useState(false);

    const handleInputChange = (field: keyof FormData, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: '' }));
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = Array.from(e.target.files || []);
        const validFiles = selectedFiles.filter((file) => {
            const validTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword'];
            return validTypes.includes(file.type) && file.size <= 10 * 1024 * 1024; // 10MB
        });

        setFormData((prev) => ({ ...prev, files: [...prev.files, ...validFiles].slice(0, 5) }));
    };

    const removeFile = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            files: prev.files.filter((_, i) => i !== index),
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setProcessing(true);

        const submitData = new FormData();
        Object.entries(formData).forEach(([key, value]) => {
            if (key === 'files') {
                (value as File[]).forEach((file, index) => {
                    submitData.append(`files[${index}]`, file);
                });
            } else {
                submitData.append(key, value as string);
            }
        });

        router.post('/projects', submitData, {
            onError: (errors) => {
                setErrors(errors);
                setProcessing(false);
            },
            onSuccess: () => {
                setProcessing(false);
                toast({
                    variant: 'success',
                    title: 'Success!',
                    description: 'Project posted successfully! You can now view it on your dashboard.',
                });
                // Force a redirect to dashboard to ensure fresh data
                router.visit('/dashboard');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Post a Project" />

            <div className="mx-auto max-w-4xl">
                <Card>
                    <CardHeader>
                        <CardTitle>Post a New Project</CardTitle>
                        <CardDescription>Create a detailed project listing to find the perfect researcher for your academic needs.</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Title */}
                            <div className="space-y-2">
                                <Label htmlFor="title">Project Title *</Label>
                                <Input
                                    id="title"
                                    value={formData.title}
                                    onChange={(e) => handleInputChange('title', e.target.value)}
                                    placeholder="Enter a clear, descriptive title for your project"
                                    className={errors.title ? 'border-red-500' : ''}
                                />
                                {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
                            </div>

                            {/* Category and Academic Level */}
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="category">Category *</Label>
                                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                                        <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {categories.map((category) => (
                                                <SelectItem key={category} value={category}>
                                                    {category}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="academic_level">Academic Level *</Label>
                                    <Select value={formData.academic_level} onValueChange={(value) => handleInputChange('academic_level', value)}>
                                        <SelectTrigger className={errors.academic_level ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select level" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {academicLevels.map((level) => (
                                                <SelectItem key={level} value={level}>
                                                    {level}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.academic_level && <p className="text-sm text-red-500">{errors.academic_level}</p>}
                                </div>
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description">Project Description *</Label>
                                <Textarea
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) => handleInputChange('description', e.target.value)}
                                    placeholder="Provide a detailed description of your project requirements, objectives, and expectations..."
                                    rows={6}
                                    className={errors.description ? 'border-red-500' : ''}
                                />
                                <p className="text-sm text-muted-foreground">Minimum 50 characters</p>
                                {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                            </div>

                            {/* Requirements */}
                            <div className="space-y-2">
                                <Label htmlFor="requirements">Specific Requirements</Label>
                                <Textarea
                                    id="requirements"
                                    value={formData.requirements}
                                    onChange={(e) => handleInputChange('requirements', e.target.value)}
                                    placeholder="List any specific requirements, methodologies, or constraints..."
                                    rows={4}
                                />
                            </div>

                            {/* Budget */}
                            <div className="space-y-4">
                                <Label>Budget Information</Label>

                                <div className="space-y-2">
                                    <Label htmlFor="budget_type">Budget Type *</Label>
                                    <Select value={formData.budget_type} onValueChange={(value) => handleInputChange('budget_type', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {budgetTypes.map((type) => (
                                                <SelectItem key={type.value} value={type.value}>
                                                    {type.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {formData.budget_type !== 'negotiable' && (
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="budget_min">
                                                {formData.budget_type === 'hourly' ? 'Hourly Rate ($)' : 'Minimum Budget ($)'}
                                            </Label>
                                            <Input
                                                id="budget_min"
                                                type="number"
                                                min="0"
                                                step="0.01"
                                                value={formData.budget_min}
                                                onChange={(e) => handleInputChange('budget_min', e.target.value)}
                                                placeholder="0.00"
                                            />
                                        </div>

                                        {formData.budget_type === 'fixed' && (
                                            <div className="space-y-2">
                                                <Label htmlFor="budget_max">Maximum Budget ($)</Label>
                                                <Input
                                                    id="budget_max"
                                                    type="number"
                                                    min="0"
                                                    step="0.01"
                                                    value={formData.budget_max}
                                                    onChange={(e) => handleInputChange('budget_max', e.target.value)}
                                                    placeholder="0.00"
                                                />
                                            </div>
                                        )}
                                    </div>
                                )}
                                {errors.budget_max && <p className="text-sm text-red-500">{errors.budget_max}</p>}
                            </div>

                            {/* Deadline */}
                            <div className="space-y-2">
                                <Label htmlFor="deadline">Deadline</Label>
                                <Input
                                    id="deadline"
                                    type="date"
                                    value={formData.deadline}
                                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                                    min={new Date().toISOString().split('T')[0]}
                                    className={errors.deadline ? 'border-red-500' : ''}
                                />
                                {errors.deadline && <p className="text-sm text-red-500">{errors.deadline}</p>}
                            </div>

                            {/* File Upload */}
                            <div className="space-y-4">
                                <Label>Project Files</Label>
                                <div className="rounded-lg border-2 border-dashed border-muted-foreground/25 p-6">
                                    <div className="text-center">
                                        <Upload className="mx-auto h-12 w-12 text-muted-foreground/50" />
                                        <div className="mt-4">
                                            <Label htmlFor="files" className="cursor-pointer">
                                                <span className="text-sm font-medium text-primary hover:text-primary/80">Click to upload files</span>
                                                <Input
                                                    id="files"
                                                    type="file"
                                                    multiple
                                                    accept=".pdf,.docx,.doc"
                                                    onChange={handleFileChange}
                                                    className="hidden"
                                                />
                                            </Label>
                                            <p className="mt-1 text-sm text-muted-foreground">PDF and Word documents only. Max 5 files, 10MB each.</p>
                                        </div>
                                    </div>
                                </div>

                                {/* File List */}
                                {formData.files.length > 0 && (
                                    <div className="space-y-2">
                                        {formData.files.map((file, index) => (
                                            <div key={index} className="flex items-center justify-between rounded-lg bg-muted p-3">
                                                <div className="flex items-center space-x-3">
                                                    <FileText className="h-5 w-5 text-muted-foreground" />
                                                    <div>
                                                        <p className="text-sm font-medium">{file.name}</p>
                                                        <p className="text-xs text-muted-foreground">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                                    </div>
                                                </div>
                                                <Button type="button" variant="ghost" size="sm" onClick={() => removeFile(index)}>
                                                    <X className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        ))}
                                    </div>
                                )}
                                {errors.files && <p className="text-sm text-red-500">{errors.files}</p>}
                            </div>

                            {/* Submit Button */}
                            <div className="flex justify-end space-x-4">
                                <Button type="button" variant="outline" onClick={() => router.visit('/dashboard')}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Posting...' : 'Post Project'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
