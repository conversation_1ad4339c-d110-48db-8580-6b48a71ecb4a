<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Rating;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class RatingController extends Controller
{
    /**
     * Show rating form for a completed project.
     */
    public function create(Project $project)
    {
        $user = Auth::user();

        // Check if project is completed
        if ($project->status !== 'completed') {
            return redirect()->back()->withErrors(['error' => 'Can only rate completed projects.']);
        }

        // Check if user is involved in the project (client or has accepted bid)
        $acceptedBid = $project->bids()->where('status', 'accepted')->first();
        $isClient = $project->user_id === $user->id;
        $isFreelancer = $acceptedBid && $acceptedBid->user_id === $user->id;

        if (! $isClient && ! $isFreelancer) {
            return redirect()->back()->withErrors(['error' => 'You are not authorized to rate this project.']);
        }

        // Check if user has already rated
        $existingRating = Rating::where('project_id', $project->id)
            ->where(function ($query) use ($user, $isClient) {
                if ($isClient) {
                    $query->where('client_id', $user->id)->where('rated_by', 'client');
                } else {
                    $query->where('freelancer_id', $user->id)->where('rated_by', 'freelancer');
                }
            })
            ->first();

        if ($existingRating) {
            return redirect()->back()->withErrors(['error' => 'You have already rated this project.']);
        }

        $otherParty = $isClient ? $acceptedBid->user : $project->user;

        return Inertia::render('Ratings/Create', [
            'project' => $project->load('user'),
            'otherParty' => $otherParty,
            'userRole' => $isClient ? 'client' : 'freelancer',
        ]);
    }

    /**
     * Store a new rating.
     */
    public function store(Request $request, Project $project)
    {
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000',
        ]);

        $user = Auth::user();

        // Check if project is completed
        if ($project->status !== 'completed') {
            return back()->withErrors(['error' => 'Can only rate completed projects.']);
        }

        // Determine user role
        $acceptedBid = $project->bids()->where('status', 'accepted')->first();
        $isClient = $project->user_id === $user->id;
        $isFreelancer = $acceptedBid && $acceptedBid->user_id === $user->id;

        if (! $isClient && ! $isFreelancer) {
            return back()->withErrors(['error' => 'You are not authorized to rate this project.']);
        }

        // Check if user has already rated
        $existingRating = Rating::where('project_id', $project->id)
            ->where(function ($query) use ($user, $isClient) {
                if ($isClient) {
                    $query->where('client_id', $user->id)->where('rated_by', 'client');
                } else {
                    $query->where('freelancer_id', $user->id)->where('rated_by', 'freelancer');
                }
            })
            ->first();

        if ($existingRating) {
            return back()->withErrors(['error' => 'You have already rated this project.']);
        }

        // Create rating
        Rating::create([
            'project_id' => $project->id,
            'client_id' => $project->user_id,
            'freelancer_id' => $acceptedBid->user_id,
            'rated_by' => $isClient ? 'client' : 'freelancer',
            'rated_for' => $isClient ? 'freelancer' : 'client',
            'rating' => $request->rating,
            'review' => $request->review,
        ]);

        return redirect()->route('projects.show', $project)
            ->with('success', 'Rating submitted successfully!');
    }

    /**
     * Show ratings for a user.
     */
    public function userRatings(User $user)
    {
        $ratings = Rating::where(function ($query) use ($user) {
            $query->where('client_id', $user->id)->where('rated_for', 'client')
                ->orWhere('freelancer_id', $user->id)->where('rated_for', 'freelancer');
        })
            ->with(['project', 'client', 'freelancer'])
            ->latest()
            ->paginate(10);

        $stats = [
            'total_ratings' => $ratings->total(),
            'average_rating' => $user->averageRating(),
            'rating_breakdown' => [
                5 => Rating::where(function ($query) use ($user) {
                    $query->where('client_id', $user->id)->where('rated_for', 'client')
                        ->orWhere('freelancer_id', $user->id)->where('rated_for', 'freelancer');
                })->where('rating', 5)->count(),
                4 => Rating::where(function ($query) use ($user) {
                    $query->where('client_id', $user->id)->where('rated_for', 'client')
                        ->orWhere('freelancer_id', $user->id)->where('rated_for', 'freelancer');
                })->where('rating', 4)->count(),
                3 => Rating::where(function ($query) use ($user) {
                    $query->where('client_id', $user->id)->where('rated_for', 'client')
                        ->orWhere('freelancer_id', $user->id)->where('rated_for', 'freelancer');
                })->where('rating', 3)->count(),
                2 => Rating::where(function ($query) use ($user) {
                    $query->where('client_id', $user->id)->where('rated_for', 'client')
                        ->orWhere('freelancer_id', $user->id)->where('rated_for', 'freelancer');
                })->where('rating', 2)->count(),
                1 => Rating::where(function ($query) use ($user) {
                    $query->where('client_id', $user->id)->where('rated_for', 'client')
                        ->orWhere('freelancer_id', $user->id)->where('rated_for', 'freelancer');
                })->where('rating', 1)->count(),
            ],
        ];

        return Inertia::render('Ratings/UserRatings', [
            'user' => $user,
            'ratings' => $ratings,
            'stats' => $stats,
        ]);
    }
}
