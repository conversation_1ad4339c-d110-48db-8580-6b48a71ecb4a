import { Icon } from '@/components/icon';
import { Badge } from '@/components/ui/badge';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList, navigationMenuTriggerStyle } from '@/components/ui/navigation-menu';
import { cn } from '@/lib/utils';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { FolderOpen, LayoutGrid, Mail } from 'lucide-react';
import { useEffect, useState } from 'react';

const secondaryNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'My Projects',
        href: '/my-projects',
        icon: FolderOpen,
    },
    {
        title: 'Browse Projects',
        href: '/browse',
        icon: FolderOpen,
    },
    {
        title: 'Inbox',
        href: '/inbox',
        icon: Mail,
    },
];

const activeItemStyles = 'text-white bg-white/10';

export function AppSecondaryHeader() {
    const page = usePage<SharedData>();
    const [unreadMessagesCount, setUnreadMessagesCount] = useState(0);

    // TODO: Implement real API call when message system has unread count endpoint
    const fetchUnreadMessageCount = async () => {
        try {
            // Placeholder for future API implementation
            // const response = await fetch('/api/conversations/unread-count');
            // const data = await response.json();
            // setUnreadMessagesCount(data.count);

            // For now, set to 0 since no users have new chats yet
            setUnreadMessagesCount(0);
        } catch (error) {
            console.error('Failed to fetch unread message count:', error);
            setUnreadMessagesCount(0);
        }
    };

    useEffect(() => {
        fetchUnreadMessageCount();
    }, []);

    return (
        <div className="border-b border-sidebar-border/80" style={{ backgroundColor: '#232A35', color: '#FFFFFF' }}>
            <div className="mx-auto flex h-12 items-center px-4 md:max-w-7xl">
                <NavigationMenu className="flex h-full items-stretch">
                    <NavigationMenuList className="flex h-full items-stretch space-x-1 sm:space-x-2">
                        {secondaryNavItems.map((item, index) => (
                            <NavigationMenuItem key={index} className="relative flex h-full items-center">
                                <Link
                                    href={item.href}
                                    className={cn(
                                        navigationMenuTriggerStyle(),
                                        page.url === item.href && activeItemStyles,
                                        'relative h-8 cursor-pointer px-2 text-xs text-white hover:bg-white/10 hover:text-white sm:px-3 sm:text-sm',
                                    )}
                                >
                                    {item.icon && <Icon iconNode={item.icon} className="mr-1 h-3.5 w-3.5 sm:mr-2" />}
                                    <span className="hidden sm:inline">{item.title}</span>
                                    <span className="sm:hidden">
                                        {item.title === 'My Projects' ? 'Mine' : item.title === 'Browse Projects' ? 'Browse' : item.title}
                                    </span>
                                    {item.title === 'Inbox' && unreadMessagesCount > 0 && (
                                        <Badge className="ml-1 flex h-4 w-4 items-center justify-center rounded-full bg-destructive p-0 text-xs text-destructive-foreground sm:ml-2 sm:h-5 sm:w-5">
                                            {unreadMessagesCount > 99 ? '99+' : unreadMessagesCount}
                                        </Badge>
                                    )}
                                </Link>
                                {page.url === item.href && <div className="absolute bottom-0 left-0 h-0.5 w-full translate-y-px bg-white"></div>}
                            </NavigationMenuItem>
                        ))}
                    </NavigationMenuList>
                </NavigationMenu>
            </div>
        </div>
    );
}
