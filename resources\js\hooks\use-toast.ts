import { toast as sonnerToast } from 'sonner'

interface ToastOptions {
  variant?: 'default' | 'success' | 'error' | 'warning' | 'info'
  title?: string
  description?: string
}

function toast({ variant = 'default', title, description }: ToastOptions) {
  const message = title || description || ''

  switch (variant) {
    case 'success':
      return sonnerToast.success(message, {
        description: title && description ? description : undefined,
      })
    case 'error':
      return sonnerToast.error(message, {
        description: title && description ? description : undefined,
      })
    case 'warning':
      return sonnerToast.warning(message, {
        description: title && description ? description : undefined,
      })
    case 'info':
      return sonnerToast.info(message, {
        description: title && description ? description : undefined,
      })
    default:
      return sonnerToast(message, {
        description: title && description ? description : undefined,
      })
  }
}

function useToast() {
  return {
    toast,
    dismiss: sonnerToast.dismiss,
  }
}

export { useToast, toast }
