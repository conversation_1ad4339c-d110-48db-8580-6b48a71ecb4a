<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $skills = [
            ['Laravel', 'PHP', 'JavaScript'],
            ['React', 'Vue.js', 'Node.js'],
            ['Python', 'Data Science', 'Machine Learning'],
            ['Academic Writing', 'Research', 'Documentation'],
            ['Business Analysis', 'Project Management'],
            ['Engineering', 'AutoCAD', 'MATLAB'],
            ['Marketing', 'Social Media', 'Content Creation'],
            ['Graphic Design', 'Photoshop', 'Illustrator'],
        ];

        $educationLevels = [
            'BSc Computer Science',
            'BSc Engineering',
            'BSc Business Administration',
            'MSc Software Engineering',
            'MSc Data Science',
            'MBA',
            'BSc Information Technology',
            'BSc Mathematics',
            'BSc Physics',
            'BA English Literature',
            'BSc Economics',
            'BSc Accounting',
        ];

        $locations = [
            'Accra, Ghana',
            'Kumasi, Ghana',
            'Tamale, Ghana',
            'Takoradi, Ghana',
            'Cape Coast, Ghana',
            'Ho, Ghana',
            'Sunyani, Ghana',
            'Koforidua, Ghana',
        ];

        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'role' => 'user',
            'status' => fake()->randomElement(['active', 'active', 'active', 'suspended']), // 75% active
            'wallet_balance' => fake()->randomFloat(2, 0, 500),
            'bio' => fake()->paragraph(2),
            'skills' => fake()->randomElement($skills),
            'education' => fake()->randomElement($educationLevels),
            'is_verified' => fake()->boolean(30), // 30% verified
            'phone' => '+233'.fake()->numerify('#########'),
            'location' => fake()->randomElement($locations),
            'last_activity' => fake()->dateTimeBetween('-7 days', 'now'),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
