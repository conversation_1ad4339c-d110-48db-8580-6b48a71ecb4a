import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { AlertTriangle, ArrowLeft, Banknote, Clock, DollarSign } from 'lucide-react';
import { FormEvent, useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    wallet_balance: number;
}

interface Props {
    user: User;
    balance: number;
}

const breadcrumbs = [
    { title: 'Wallet', href: '/wallet/balance' },
    { title: 'Withdraw', href: '/wallet/withdraw' },
];

export default function Withdraw({ user, balance }: Props) {
    const [showConfirmation, setShowConfirmation] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm({
        amount: '',
        bank_name: '',
        account_number: '',
        account_name: '',
    });

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();

        if (showConfirmation) {
            post(route('wallet.withdraw.process'), {
                onSuccess: () => {
                    reset();
                    setShowConfirmation(false);
                },
                onError: () => {
                    setShowConfirmation(false);
                },
            });
        } else {
            setShowConfirmation(true);
        }
    };

    const handleCancel = () => {
        setShowConfirmation(false);
    };

    const withdrawalAmount = parseFloat(data.amount) || 0;
    const isValidAmount = withdrawalAmount > 0 && withdrawalAmount <= Number(balance);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Withdraw Funds" />

            <div className="mx-auto max-w-2xl space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" asChild>
                        <a href="/wallet/balance">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Wallet
                        </a>
                    </Button>
                </div>

                <div className="space-y-2">
                    <h1 className="text-3xl font-bold">Withdraw Funds</h1>
                    <p className="text-muted-foreground">Transfer money from your wallet to your bank account</p>
                </div>

                {/* Current Balance */}
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Available Balance</p>
                                <p className="text-2xl font-bold">₵{Number(balance).toFixed(2)}</p>
                            </div>
                            <div className="rounded-full bg-primary/10 p-3">
                                <DollarSign className="h-6 w-6 text-primary" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Withdrawal Notice */}
                <Alert>
                    <Clock className="h-4 w-4" />
                    <AlertDescription>
                        Withdrawal requests are processed within 1-3 business days. You'll receive a confirmation email once processed.
                    </AlertDescription>
                </Alert>

                {/* Withdrawal Form */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Banknote className="h-5 w-5" />
                            {showConfirmation ? 'Confirm Withdrawal' : 'Withdrawal Details'}
                        </CardTitle>
                        <CardDescription>
                            {showConfirmation
                                ? 'Please review your withdrawal details before confirming.'
                                : 'Enter the amount and bank account details for your withdrawal.'}
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {showConfirmation ? (
                            /* Confirmation View */
                            <div className="space-y-4">
                                <div className="space-y-3 rounded-lg bg-muted p-4">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Amount:</span>
                                        <span className="font-medium">₵{withdrawalAmount.toFixed(2)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Bank:</span>
                                        <span className="font-medium">{data.bank_name}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Account Number:</span>
                                        <span className="font-medium">{data.account_number}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Account Name:</span>
                                        <span className="font-medium">{data.account_name}</span>
                                    </div>
                                    <Separator />
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Remaining Balance:</span>
                                        <span className="font-medium">₵{(Number(balance) - withdrawalAmount).toFixed(2)}</span>
                                    </div>
                                </div>

                                <Alert>
                                    <AlertTriangle className="h-4 w-4" />
                                    <AlertDescription>
                                        Please ensure your bank account details are correct. Incorrect details may delay processing.
                                    </AlertDescription>
                                </Alert>

                                <div className="flex gap-3">
                                    <Button type="button" variant="outline" onClick={handleCancel} className="flex-1" disabled={processing}>
                                        Cancel
                                    </Button>
                                    <Button onClick={handleSubmit} className="flex-1" disabled={processing}>
                                        {processing ? 'Processing...' : 'Confirm Withdrawal'}
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            /* Form View */
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Amount */}
                                <div className="space-y-2">
                                    <Label htmlFor="amount">Withdrawal Amount</Label>
                                    <div className="relative">
                                        <span className="absolute top-1/2 left-3 -translate-y-1/2 text-muted-foreground">₵</span>
                                        <Input
                                            id="amount"
                                            type="number"
                                            step="0.01"
                                            min="1"
                                            max={Number(balance)}
                                            placeholder="0.00"
                                            value={data.amount}
                                            onChange={(e) => setData('amount', e.target.value)}
                                            className="pl-8"
                                            required
                                        />
                                    </div>
                                    {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
                                    {withdrawalAmount > Number(balance) && (
                                        <p className="text-sm text-destructive">Amount exceeds available balance</p>
                                    )}
                                </div>

                                <Separator />

                                {/* Bank Details */}
                                <div className="space-y-4">
                                    <h3 className="font-medium">Bank Account Details</h3>

                                    <div className="space-y-2">
                                        <Label htmlFor="bank_name">Bank Name</Label>
                                        <Input
                                            id="bank_name"
                                            type="text"
                                            placeholder="e.g., Ghana Commercial Bank"
                                            value={data.bank_name}
                                            onChange={(e) => setData('bank_name', e.target.value)}
                                            required
                                        />
                                        {errors.bank_name && <p className="text-sm text-destructive">{errors.bank_name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="account_number">Account Number</Label>
                                        <Input
                                            id="account_number"
                                            type="text"
                                            placeholder="Enter your account number"
                                            value={data.account_number}
                                            onChange={(e) => setData('account_number', e.target.value)}
                                            required
                                        />
                                        {errors.account_number && <p className="text-sm text-destructive">{errors.account_number}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="account_name">Account Holder Name</Label>
                                        <Input
                                            id="account_name"
                                            type="text"
                                            placeholder="Full name as on bank account"
                                            value={data.account_name}
                                            onChange={(e) => setData('account_name', e.target.value)}
                                            required
                                        />
                                        {errors.account_name && <p className="text-sm text-destructive">{errors.account_name}</p>}
                                    </div>
                                </div>

                                {/* Submit Button */}
                                <Button
                                    type="submit"
                                    className="w-full"
                                    size="lg"
                                    disabled={processing || !isValidAmount || !data.bank_name || !data.account_number || !data.account_name}
                                >
                                    <Banknote className="mr-2 h-4 w-4" />
                                    Review Withdrawal
                                </Button>
                            </form>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
