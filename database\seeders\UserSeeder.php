<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create or update Super Admin (you)
        User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => '<PERSON>',
            'password' => Hash::make('password123'),
            'role' => 'super_admin',
            'status' => 'active',
            'email_verified_at' => now(),
            // Do not seed wallets with funds; keep zero in all environments
            'wallet_balance' => 0,
            'bio' => 'Super Administrator and Developer of TheSylink platform.',
            'skills' => ['Laravel', 'React', 'Project Management', 'System Administration'],
            'education' => 'Computer Science',
            'is_verified' => true,
            'phone' => '+233247648200',
            'location' => 'Ghana',
            'last_activity' => now(),
        ]);

        // Create or update one regular admin
        User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Admin User',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
            'wallet_balance' => 0,
            'bio' => 'Platform Administrator helping manage the TheSylink community.',
            'skills' => ['Project Management', 'Customer Support', 'Content Moderation'],
            'education' => 'Business Administration',
            'is_verified' => true,
            'phone' => '+1234567891',
            'location' => 'Ghana',
            'last_activity' => now(),
        ]);
    }
}
