<?php

namespace App\Providers;

use App\Listeners\SendWelcomeEmail;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Configure HTTP client for local development SSL issues
        if ($this->app->environment('local')) {
            Http::globalOptions([
                'verify' => env('CURLOPT_SSL_VERIFYPEER', false),
            ]);
        }

        // Register event listeners
        Event::listen(Registered::class, SendWelcomeEmail::class);
    }
}
