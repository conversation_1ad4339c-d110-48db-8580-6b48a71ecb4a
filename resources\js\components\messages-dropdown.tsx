import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { Link } from '@inertiajs/react';
import { MessageCircle, Search } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Message {
    id: number;
    sender: {
        name: string;
        avatar?: string;
        isVerified: boolean;
    };
    preview: string;
    time: string;
    unread: boolean;
    projectTitle?: string;
}

export function MessagesDropdown() {
    const [messages, setMessages] = useState<Message[]>([]);
    const [loading, setLoading] = useState(false);

    const unreadCount = messages.filter((m) => m.unread).length;

    // TODO: Implement real API call when message system is ready
    const fetchRecentMessages = async () => {
        setLoading(true);
        try {
            // Placeholder for future API implementation
            // const response = await fetch('/api/messages/recent');
            // const data = await response.json();
            // setMessages(data.messages);

            // For now, return empty array since we're removing mock data
            setMessages([]);
        } catch (error) {
            console.error('Failed to fetch messages:', error);
            setMessages([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchRecentMessages();
    }, []);

    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map((n) => n[0])
            .join('')
            .toUpperCase();
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="group relative h-9 w-9 cursor-pointer">
                    <MessageCircle className="size-5 opacity-70 group-hover:opacity-100" />
                    {unreadCount > 0 && (
                        <Badge className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive p-0 text-xs text-destructive-foreground">
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </Badge>
                    )}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-96 border border-border bg-background p-0 shadow-lg" align="end" side="bottom" sideOffset={8}>
                {/* Header */}
                <div className="flex items-center justify-between border-b border-border p-4">
                    <h3 className="text-lg font-semibold text-foreground">Recent Messages</h3>
                    <Button asChild variant="ghost" size="sm" className="text-primary hover:text-primary/80">
                        <Link href="/inbox">View All</Link>
                    </Button>
                </div>

                {/* Messages List */}
                <ScrollArea className="h-96">
                    {loading ? (
                        <div className="flex h-64 flex-col items-center justify-center p-6 text-center">
                            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
                            <p className="mt-4 text-sm text-muted-foreground">Loading messages...</p>
                        </div>
                    ) : messages.length > 0 ? (
                        <div className="divide-y divide-border">
                            {messages.map((message) => (
                                <div
                                    key={message.id}
                                    className={cn(
                                        'cursor-pointer p-4 transition-colors hover:bg-muted/50',
                                        message.unread && 'border-l-2 border-l-primary bg-primary/5',
                                    )}
                                >
                                    <div className="flex items-start gap-3">
                                        <div className="relative">
                                            <Avatar className="h-10 w-10">
                                                <AvatarImage src={message.sender.avatar} alt={message.sender.name} />
                                                <AvatarFallback className="bg-muted text-sm text-muted-foreground">
                                                    {getInitials(message.sender.name)}
                                                </AvatarFallback>
                                            </Avatar>
                                            {message.sender.isVerified && (
                                                <div className="absolute -right-1 -bottom-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary">
                                                    <svg className="h-2.5 w-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                        <path
                                                            fillRule="evenodd"
                                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                            clipRule="evenodd"
                                                        />
                                                    </svg>
                                                </div>
                                            )}
                                        </div>
                                        <div className="min-w-0 flex-1">
                                            <div className="mb-1 flex items-center gap-2">
                                                <h4 className="truncate text-sm font-medium text-foreground">{message.sender.name}</h4>
                                                <span className="text-xs text-muted-foreground">{message.time}</span>
                                            </div>
                                            {message.projectTitle && (
                                                <p className="mb-1 text-xs text-muted-foreground">Project: {message.projectTitle}</p>
                                            )}
                                            <p className="line-clamp-2 text-sm text-muted-foreground">{message.preview}</p>
                                        </div>
                                        {message.unread && <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-primary" />}
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="flex h-64 flex-col items-center justify-center p-6 text-center">
                            <MessageCircle className="mb-4 h-12 w-12 text-muted-foreground/50" />
                            <h4 className="mb-2 text-lg font-medium text-foreground">No messages</h4>
                            <p className="mb-4 text-sm text-muted-foreground">
                                You don't have any messages as of this moment.
                                <br />
                                Would you like to search for a project instead?
                            </p>
                            <Button asChild variant="outline" size="sm">
                                <Link href="/browse">
                                    <Search className="mr-2 h-4 w-4" />
                                    Search Project
                                </Link>
                            </Button>
                        </div>
                    )}
                </ScrollArea>

                {/* Footer */}
                {messages.length > 0 && (
                    <div className="border-t border-border p-3">
                        <Button asChild variant="ghost" className="w-full text-sm">
                            <Link href="/inbox">View All Messages</Link>
                        </Button>
                    </div>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
