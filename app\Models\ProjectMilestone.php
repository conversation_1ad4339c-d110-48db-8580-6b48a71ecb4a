<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProjectMilestone extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'title',
        'description',
        'order_index',
        'payment_percentage',
        'payment_amount',
        'status',
        'started_at',
        'submitted_at',
        'approved_at',
        'paid_at',
        'submission_notes',
        'approval_notes',
    ];

    protected $casts = [
        'payment_percentage' => 'decimal:2',
        'payment_amount' => 'decimal:2',
        'started_at' => 'datetime',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function escrowTransactions(): HasMany
    {
        return $this->hasMany(EscrowTransaction::class, 'milestone_id');
    }

    public function platformCommissions(): HasMany
    {
        return $this->hasMany(PlatformCommission::class, 'milestone_id');
    }

    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isSubmitted(): bool
    {
        return in_array($this->status, ['submitted', 'approved', 'paid']);
    }

    public function canBeStarted(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeSubmitted(): bool
    {
        return $this->status === 'in_progress';
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'submitted';
    }
}
