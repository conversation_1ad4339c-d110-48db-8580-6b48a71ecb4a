<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WelcomeUser extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;

    /**
     * Create a new notification instance.
     */
    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Welcome to Thesylink - Your Academic Projects Platform!')
            ->greeting('Hello '.$this->user->name.'!')
            ->line('Welcome to Thesylink, the premier platform connecting students and academic professionals for collaborative learning and project completion.')
            ->line('🎓 **What you can do on Thesylink:**')
            ->line('• **Browse Projects**: Discover academic projects that match your skills and interests')
            ->line('• **Post Projects**: Share your academic challenges and get expert help')
            ->line('• **Collaborate**: Work with verified academic professionals and fellow students')
            ->line('• **Secure Payments**: Our escrow system ensures safe transactions for all parties')
            ->line('• **Build Your Portfolio**: Showcase your expertise and build your academic reputation')
            ->line('')
            ->line('🚀 **Ready to get started?**')
            ->action('Explore Projects Now', url('/browse'))
            ->line('**Need help getting started?**')
            ->line('• Complete your profile to attract quality project opportunities')
            ->line('• Browse available projects in your field of expertise')
            ->line('• Post your first project if you need academic assistance')
            ->line('')
            ->line('If you have any questions or need support, our team is here to help. Simply reply to this email or contact us through the platform.')
            ->line('')
            ->line('Welcome to the Thesylink community!')
            ->salutation('Best regards,  
The Thesylink Team  
*Connecting minds, creating futures*');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
