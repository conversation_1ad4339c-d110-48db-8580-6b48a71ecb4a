<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'slug',
        'description',
        'requirements',
        'budget_min',
        'budget_max',
        'budget_type',
        'deadline',
        'category',
        'academic_level',
        'status',
        'file_count',
        'assigned_freelancer_id',
        'accepted_bid_amount',
        'assigned_at',
        'escrow_amount',
        'escrow_status',
        'total_milestones',
        'completed_milestones',
        'total_released',
        'total_commission',
        'escrow_created_at',
    ];

    protected $casts = [
        'deadline' => 'date',
        'budget_min' => 'decimal:2',
        'budget_max' => 'decimal:2',
        'accepted_bid_amount' => 'decimal:2',
        'assigned_at' => 'datetime',
        'escrow_amount' => 'decimal:2',
        'total_released' => 'decimal:2',
        'total_commission' => 'decimal:2',
        'escrow_created_at' => 'datetime',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($project) {
            if (empty($project->slug)) {
                $project->slug = static::generateUniqueSlug($project->title);
            }
        });

        static::updating(function ($project) {
            if ($project->isDirty('title') && empty($project->slug)) {
                $project->slug = static::generateUniqueSlug($project->title);
            }
        });
    }

    public static function generateUniqueSlug(string $title): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->exists()) {
            $slug = $originalSlug.'-'.$counter;
            $counter++;
        }

        return $slug;
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function files(): HasMany
    {
        return $this->hasMany(ProjectFile::class);
    }

    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class);
    }

    public function assignedFreelancer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_freelancer_id');
    }

    public function acceptedBid(): HasOne
    {
        return $this->hasOne(Bid::class)->where('status', 'accepted');
    }

    public function milestones(): HasMany
    {
        return $this->hasMany(ProjectMilestone::class)->orderBy('order_index');
    }

    public function escrowTransactions(): HasMany
    {
        return $this->hasMany(EscrowTransaction::class);
    }

    public function platformCommissions(): HasMany
    {
        return $this->hasMany(PlatformCommission::class);
    }

    public function getBudgetDisplayAttribute(): string
    {
        if ($this->budget_type === 'negotiable') {
            return 'Negotiable';
        }

        if ($this->budget_min && $this->budget_max) {
            return '₵'.number_format((float) $this->budget_min, 2).' - ₵'.number_format((float) $this->budget_max, 2);
        }

        if ($this->budget_min) {
            return '₵'.number_format((float) $this->budget_min, 2);
        }

        return 'Not specified';
    }
}
