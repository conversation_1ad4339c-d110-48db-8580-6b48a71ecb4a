<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained('users')->onDelete('cascade'); // Project owner
            $table->foreignId('freelancer_id')->constrained('users')->onDelete('cascade'); // Bidder/Freelancer
            $table->timestamp('last_message_at')->nullable();
            $table->timestamps();

            // Ensure unique conversation per project-client-freelancer combination
            $table->unique(['project_id', 'client_id', 'freelancer_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversations');
    }
};
