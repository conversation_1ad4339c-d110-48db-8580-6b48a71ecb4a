<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_milestones', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('title'); // e.g., "Chapter 1: Introduction"
            $table->text('description')->nullable();
            $table->integer('order_index'); // 1, 2, 3, etc.
            $table->decimal('payment_percentage', 5, 2); // e.g., 12.50 for 12.5%
            $table->decimal('payment_amount', 10, 2)->nullable(); // calculated amount
            $table->enum('status', ['pending', 'in_progress', 'submitted', 'approved', 'paid'])->default('pending');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('submitted_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->text('submission_notes')->nullable();
            $table->text('approval_notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_milestones');
    }
};
