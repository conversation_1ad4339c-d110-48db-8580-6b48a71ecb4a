@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    /* Brand colors extracted from design */
    --primary: #465DFB; /* blue */
    --primary-foreground: #ffffff;
    --secondary: #895DEB; /* purple */
    --secondary-foreground: #ffffff;
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: #86C67A; /* green */
    --accent-foreground: #ffffff;
    --destructive: #E7623D; /* orange / destructive */
    --destructive-foreground: #ffffff;
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.87 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    /* Map chart colors to brand palette */
    --chart-2: #6AC75A; /* green */
    --chart-3: #895DEB; /* purple */
    --chart-4: #F9C45C; /* yellow */
    --chart-5: #E7623D; /* orange */
    --radius: 0.625rem;
    /* Left sidenav color in light mode (provided) */
    --sidebar: #232E51;
    --sidebar-foreground: #ffffff;
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.87 0 0);

    /* Status colors (light mode tokens) */
    --status-completed: #6AC75A;
    --status-completed-bg: #E9F7E7;
    --status-canceled: #FF6D43;
    --status-canceled-bg: #FFE9E3;
    --status-scheduled: #F9C45C;
    --status-scheduled-bg: #FEF6E7;
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    /* Dark mode maps use the same brand tokens to ensure consistent branding */
    --primary: #465DFB;
    --primary-foreground: #ffffff;
    --secondary: #895DEB;
    --secondary-foreground: #ffffff;
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: #86C67A;
    --accent-foreground: #ffffff;
    --destructive: #E7623D;
    --destructive-foreground: #ffffff;
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    /* Keep dark sidebar as original dark mapping (leave unchanged) */
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);

    /* Status colors for dark mode (use same hues; backgrounds can be slightly darker if needed) */
    --status-completed: #6AC75A;
    --status-completed-bg: rgba(106,199,90,0.12);
    --status-canceled: #FF6D43;
    --status-canceled-bg: rgba(255,109,67,0.08);
    --status-scheduled: #F9C45C;
    --status-scheduled-bg: rgba(249,196,92,0.08);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

@layer components {
    /* Status badge utilities */
    .badge-completed {
        background-color: var(--status-completed-bg);
        color: var(--status-completed);
    }
    
    .badge-canceled {
        background-color: var(--status-canceled-bg);
        color: var(--status-canceled);
    }
    
    .badge-scheduled {
        background-color: var(--status-scheduled-bg);
        color: var(--status-scheduled);
    }
}
