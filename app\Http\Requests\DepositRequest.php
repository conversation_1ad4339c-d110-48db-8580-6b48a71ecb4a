<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DepositRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() !== null;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => [
                'required',
                'numeric',
                'min:1',
                'max:1000000',
                'regex:/^\d+(\.\d{1,2})?$/', // Allow up to 2 decimal places
            ],
            'email' => [
                'required',
                'email',
                'max:255',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'Please enter the amount you want to deposit.',
            'amount.numeric' => 'The deposit amount must be a valid number.',
            'amount.min' => 'The minimum deposit amount is ₵1.00.',
            'amount.max' => 'The maximum deposit amount is ₵1,000,000.00.',
            'amount.regex' => 'The deposit amount can have at most 2 decimal places.',
            'email.required' => 'Please provide your email address.',
            'email.email' => 'Please provide a valid email address.',
            'email.max' => 'The email address is too long.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'amount' => 'deposit amount',
            'email' => 'email address',
        ];
    }
}
