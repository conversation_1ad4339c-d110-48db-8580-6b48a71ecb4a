<?php

namespace App\Services;

use App\Models\EscrowTransaction;
use App\Models\PlatformCommission;
use App\Models\Project;
use App\Models\ProjectMilestone;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EscrowService
{
    public const COMMISSION_RATE = 30.00; // 30%

    /**
     * Create escrow when project is assigned to freelancer
     */
    public function createEscrow(Project $project): bool
    {
        try {
            DB::transaction(function () use ($project) {
                // Update project escrow status
                $project->update([
                    'escrow_amount' => $project->accepted_bid_amount,
                    'escrow_status' => 'held',
                    'escrow_created_at' => now(),
                ]);

                // Create hold transaction
                EscrowTransaction::create([
                    'project_id' => $project->id,
                    'client_id' => $project->user_id,
                    'freelancer_id' => $project->assigned_freelancer_id,
                    'transaction_id' => $this->generateTransactionId('hold', $project->id),
                    'type' => 'hold',
                    'amount' => $project->accepted_bid_amount,
                    'status' => 'completed',
                    'description' => 'Escrow hold for project: '.$project->title,
                    'processed_at' => now(),
                ]);

                // Create default milestones
                $this->createDefaultMilestones($project);
            });

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to create escrow', [
                'project_id' => $project->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Create default milestones for a project
     */
    private function createDefaultMilestones(Project $project): void
    {
        $milestoneCount = $project->total_milestones;
        $paymentPercentage = 100 / $milestoneCount;
        $paymentAmount = $project->accepted_bid_amount / $milestoneCount;

        for ($i = 1; $i <= $milestoneCount; $i++) {
            ProjectMilestone::create([
                'project_id' => $project->id,
                'title' => "Chapter {$i}",
                'description' => "Complete Chapter {$i} of the project",
                'order_index' => $i,
                'payment_percentage' => $paymentPercentage,
                'payment_amount' => $paymentAmount,
                'status' => 'pending',
            ]);
        }
    }

    /**
     * Release payment for a milestone
     */
    public function releaseMilestonePayment(ProjectMilestone $milestone): bool
    {
        try {
            DB::transaction(function () use ($milestone) {
                $project = $milestone->project;
                $freelancerAmount = $milestone->payment_amount * (1 - self::COMMISSION_RATE / 100);
                $commissionAmount = $milestone->payment_amount * (self::COMMISSION_RATE / 100);

                // Create release transaction
                $escrowTransaction = EscrowTransaction::create([
                    'project_id' => $project->id,
                    'milestone_id' => $milestone->id,
                    'client_id' => $project->user_id,
                    'freelancer_id' => $project->assigned_freelancer_id,
                    'transaction_id' => $this->generateTransactionId('release', $project->id),
                    'type' => 'release',
                    'amount' => $milestone->payment_amount,
                    'commission_amount' => $commissionAmount,
                    'freelancer_amount' => $freelancerAmount,
                    'status' => 'completed',
                    'description' => "Payment release for {$milestone->title}",
                    'processed_at' => now(),
                ]);

                // Create platform commission record
                PlatformCommission::create([
                    'project_id' => $project->id,
                    'milestone_id' => $milestone->id,
                    'escrow_transaction_id' => $escrowTransaction->id,
                    'amount' => $commissionAmount,
                    'commission_rate' => self::COMMISSION_RATE,
                    'status' => 'collected',
                    'collected_at' => now(),
                ]);

                // Update freelancer wallet balance
                $freelancer = User::find($project->assigned_freelancer_id);
                $freelancer->increment('wallet_balance', $freelancerAmount);

                // Update milestone status
                $milestone->update([
                    'status' => 'paid',
                    'paid_at' => now(),
                ]);

                // Update project totals
                $project->increment('completed_milestones');
                $project->increment('total_released', $milestone->payment_amount);
                $project->increment('total_commission', $commissionAmount);

                // Update escrow status
                if ($project->completed_milestones >= $project->total_milestones) {
                    $project->update(['escrow_status' => 'fully_released']);
                } else {
                    $project->update(['escrow_status' => 'partially_released']);
                }
            });

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to release milestone payment', [
                'milestone_id' => $milestone->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId(string $type, int $projectId): string
    {
        return "escrow_{$type}_".time()."_{$projectId}_".uniqid();
    }

    /**
     * Get total platform commission balance
     */
    public function getTotalCommissionBalance(): float
    {
        return PlatformCommission::where('status', 'collected')->sum('amount');
    }

    /**
     * Get available commission for withdrawal
     */
    public function getAvailableCommissionForWithdrawal(): float
    {
        return PlatformCommission::where('status', 'collected')->sum('amount');
    }

    /**
     * Process commission withdrawal
     */
    public function withdrawCommission(float $amount, User $admin): bool
    {
        try {
            DB::transaction(function () use ($amount, $admin) {
                // Mark commissions as withdrawn (FIFO basis)
                $commissions = PlatformCommission::where('status', 'collected')
                    ->orderBy('collected_at')
                    ->get();

                $remainingAmount = $amount;
                foreach ($commissions as $commission) {
                    if ($remainingAmount <= 0) {
                        break;
                    }

                    if ($commission->amount <= $remainingAmount) {
                        $commission->update([
                            'status' => 'withdrawn',
                            'withdrawn_at' => now(),
                            'withdrawn_by' => $admin->id,
                        ]);
                        $remainingAmount -= $commission->amount;
                    }
                }
            });

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to withdraw commission', [
                'amount' => $amount,
                'admin_id' => $admin->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
