import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Calendar, DollarSign, Edit, FileText, Mail, Star, TrendingUp, User, Users } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Profile', href: '/profile' },
];

interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at?: string;
    created_at: string;
    wallet_balance: number;
}

interface Project {
    id: number;
    title: string;
    slug: string;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    created_at: string;
    bids_count?: number;
    assigned_freelancer?: {
        id: number;
        name: string;
    };
}

interface Bid {
    id: number;
    amount: number;
    status: 'pending' | 'accepted' | 'rejected';
    created_at: string;
    project: {
        id: number;
        title: string;
        slug: string;
        client: {
            id: number;
            name: string;
        };
    };
}

interface Stats {
    projects_posted: number;
    projects_completed: number;
    total_bids: number;
    accepted_bids: number;
    success_rate_as_client: number;
    success_rate_as_freelancer: number;
}

interface Props {
    user: User;
    stats: Stats;
    recent_projects_posted: Project[];
    recent_bids: Bid[];
}

export default function ProfileShow({ user, stats, recent_projects_posted, recent_bids }: Props) {
    const formatCurrency = (amount: number) => `₵${Number(amount || 0).toFixed(2)}`;

    const getStatusBadge = (status: string) => {
        const variants = {
            open: { variant: 'default' as const, color: 'text-blue-600' },
            in_progress: { variant: 'secondary' as const, color: 'text-yellow-600' },
            completed: { variant: 'default' as const, color: 'text-green-600' },
            cancelled: { variant: 'destructive' as const, color: 'text-red-600' },
            pending: { variant: 'outline' as const, color: 'text-yellow-600' },
            accepted: { variant: 'default' as const, color: 'text-green-600' },
            rejected: { variant: 'destructive' as const, color: 'text-red-600' },
        };

        const config = variants[status as keyof typeof variants] || variants.open;

        return <Badge variant={config.variant}>{status.replace('_', ' ').toUpperCase()}</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="My Profile" />

            <div className="space-y-8">
                {/* Profile Header */}
                <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
                    <div className="flex items-start space-x-4">
                        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
                            <User className="h-10 w-10 text-primary" />
                        </div>
                        <div className="flex-1">
                            <h1 className="text-2xl font-bold sm:text-3xl">{user.name}</h1>
                            <div className="mt-2 flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
                                <div className="flex items-center text-sm text-muted-foreground">
                                    <Mail className="mr-1 h-4 w-4" />
                                    {user.email}
                                    {user.email_verified_at && (
                                        <Badge variant="outline" className="ml-2">
                                            Verified
                                        </Badge>
                                    )}
                                </div>
                                <div className="flex items-center text-sm text-muted-foreground">
                                    <Calendar className="mr-1 h-4 w-4" />
                                    Member since {new Date(user.created_at).toLocaleDateString()}
                                </div>
                            </div>
                        </div>
                    </div>
                    <Button asChild className="w-full sm:w-auto">
                        <Link href="/settings/profile">
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Profile
                        </Link>
                    </Button>
                </div>

                {/* Statistics Overview */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Projects Posted</p>
                                    <p className="text-2xl font-bold">{stats.projects_posted}</p>
                                    <p className="text-xs text-green-600">{stats.projects_completed} completed</p>
                                </div>
                                <FileText className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Total Bids</p>
                                    <p className="text-2xl font-bold">{stats.total_bids}</p>
                                    <p className="text-xs text-green-600">{stats.accepted_bids} accepted</p>
                                </div>
                                <TrendingUp className="h-8 w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Client Success Rate</p>
                                    <p className="text-2xl font-bold">{stats.success_rate_as_client}%</p>
                                    <p className="text-xs text-muted-foreground">Project completion</p>
                                </div>
                                <Star className="h-8 w-8 text-yellow-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Freelancer Success Rate</p>
                                    <p className="text-2xl font-bold">{stats.success_rate_as_freelancer}%</p>
                                    <p className="text-xs text-muted-foreground">Bid acceptance</p>
                                </div>
                                <Users className="h-8 w-8 text-purple-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    {/* Recent Projects Posted */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Projects Posted</CardTitle>
                            <CardDescription>Your latest project postings</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {recent_projects_posted.length > 0 ? (
                                <div className="space-y-4">
                                    {recent_projects_posted.map((project) => (
                                        <div key={project.id} className="flex items-center justify-between border-b pb-4 last:border-b-0">
                                            <div className="min-w-0 flex-1">
                                                <Link href={`/projects/${project.slug}`} className="block truncate font-medium hover:text-blue-600">
                                                    {project.title}
                                                </Link>
                                                <div className="mt-1 flex items-center gap-2">
                                                    {getStatusBadge(project.status)}
                                                    <span className="text-sm text-muted-foreground">{project.bids_count} bids</span>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm text-muted-foreground">{new Date(project.created_at).toLocaleDateString()}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="py-8 text-center text-muted-foreground">No projects posted yet</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Recent Bids */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Bids</CardTitle>
                            <CardDescription>Your latest bid submissions</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {recent_bids.length > 0 ? (
                                <div className="space-y-4">
                                    {recent_bids.map((bid) => (
                                        <div key={bid.id} className="flex items-center justify-between border-b pb-4 last:border-b-0">
                                            <div className="min-w-0 flex-1">
                                                <Link
                                                    href={`/projects/${bid.project.slug}`}
                                                    className="block truncate font-medium hover:text-blue-600"
                                                >
                                                    {bid.project.title}
                                                </Link>
                                                <div className="mt-1 flex items-center gap-2">
                                                    {getStatusBadge(bid.status)}
                                                    <span className="text-sm text-muted-foreground">{formatCurrency(bid.amount)}</span>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm text-muted-foreground">{new Date(bid.created_at).toLocaleDateString()}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="py-8 text-center text-muted-foreground">No bids submitted yet</p>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>Manage your account and activities</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                            <Button asChild variant="outline" className="h-auto p-4">
                                <Link href="/projects/create" className="flex flex-col items-center gap-2">
                                    <FileText className="h-6 w-6" />
                                    <span>Post Project</span>
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="h-auto p-4">
                                <Link href="/browse" className="flex flex-col items-center gap-2">
                                    <Users className="h-6 w-6" />
                                    <span>Browse Projects</span>
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="h-auto p-4">
                                <Link href="/wallet/transactions" className="flex flex-col items-center gap-2">
                                    <DollarSign className="h-6 w-6" />
                                    <span>View Transactions</span>
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="h-auto p-4">
                                <Link href="/analytics" className="flex flex-col items-center gap-2">
                                    <TrendingUp className="h-6 w-6" />
                                    <span>View Analytics</span>
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
